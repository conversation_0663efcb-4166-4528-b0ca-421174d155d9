# Task Context: Replicate "Close All Positions" to matrix_QP Project

## Goal
Replicate the "Close All Positions" button and its associated functionality from the current project to the parallel `matrix_QP` project.

## User Feedback
"Can you replicate it to the other matrix_QP project too?"

## Requirements
1.  **Copy Functionality:** Apply the same code changes for the "Close All Positions" feature to the `matrix_QP` project.
2.  **Update `mt5_connector.py`:** Add the `close_all_positions` method to `../matrix_QP/mt5_connector.py`.
3.  **Update `dashboard.py`:** Add the new button layout and endpoint to `../matrix_QP/dashboard.py`.
4.  **Update `style.css`:** Copy the new button styling to `../matrix_QP/assets/style.css`.

## Key Files to Modify
*   `../matrix_QP/mt5_connector.py`
*   `../matrix_QP/dashboard.py`
*   `../matrix_QP/assets/style.css`

## Reference Files (Source of Changes)
*   `./mt5_connector.py`
*   `./dashboard.py`
*   `./assets/style.css`