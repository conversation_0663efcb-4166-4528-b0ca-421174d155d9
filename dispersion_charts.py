"""
Dispersion Chart Creation Module for Matrix QP
Creates Plotly charts for dispersion analysis
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.io as pio
import logging
from datetime import datetime, timedelta, time
from typing import Dict, Optional, Tuple, List

from config import CURRENCY_COLORS, MARKET_TIMEZONE

# Configure logging
logger = logging.getLogger(__name__)


class DispersionChartCreator:
    """
    Creates dispersion-related charts for the Matrix QP dashboard
    """
    
    def __init__(self):
        """Initialize the chart creator"""
        pio.templates.default = "plotly_dark"
    
    def create_rolling_dispersion_chart(self,
                                      cumulative_returns_ts: pd.DataFrame,
                                      rolling_dispersion_df: pd.DataFrame,
                                      show_adx: bool = True,
                                      adx_window: int = 14) -> Dict:
        """
        Create a Plotly line chart showing rolling CSSD dispersion for all currency pairs

        Args:
            cumulative_returns_ts: DataFrame with raw cumulative log returns (for color determination)
            rolling_dispersion_df: DataFrame with rolling dispersion values
            show_adx: Whether to show ADX indicator overlay (default False)
            adx_window: Window size for ADX calculation (default 14)

        Returns:
            Dictionary containing figure and weight strings
        """
        if cumulative_returns_ts.empty or rolling_dispersion_df.empty:
            logger.warning("Empty data provided for rolling dispersion chart")
            return {
                'figure': go.Figure().update_layout(
                    title="Rolling Dispersion of Normalized Returns CSSD",
                    template="plotly_dark"
                ),
                'near_peak_weights_str': '',
                'star_weights_str': ''
            }
        
        # Sort pairs by their final rolling dispersion value for legend order
        final_values = rolling_dispersion_df.iloc[-1].dropna()
        sorted_pairs_by_final_value = final_values.sort_values(ascending=False).index.tolist()

        # Create figure with subplots if ADX is enabled
        if show_adx:
            from plotly.subplots import make_subplots
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=["Rolling Dispersion of Normalized Returns CSSD", "ADX (Market Sum)"],
                vertical_spacing=0.08,
                row_heights=[0.75, 0.25],
                specs=[[{"secondary_y": False}], [{"secondary_y": False}]]
            )
            has_adx_subplot = True
        else:
            fig = go.Figure()
            has_adx_subplot = False
        
        # Track near-peak and star pairs
        near_peak_pairs = {}
        star_pairs = {}
        
        for pair in sorted_pairs_by_final_value:
            y = rolling_dispersion_df[pair]
            
            # Find peak timestamp and value for this pair
            peak_idx = y.idxmax() if not y.isnull().all() else None
            peak_val = y.max() if not y.isnull().all() else np.nan
            
            # Get color based on last cumulative return value
            last_val = cumulative_returns_ts[pair].dropna()
            last_val = last_val.iloc[-1] if not last_val.empty else 0
            color = self._get_pair_color(pair, last_val)
            
            # Check if current value is close to peak value
            latest_val = y.iloc[-1] if not y.empty else np.nan
            is_peak = np.isclose(latest_val, peak_val, atol=1e-8, equal_nan=True)
            is_near_peak = (peak_val != 0 and not pd.isna(latest_val) and not pd.isna(peak_val)
                           and latest_val >= peak_val * 0.95)
            
            # Track pairs for weight calculation
            if is_near_peak:
                near_peak_pairs[pair] = last_val
            if is_peak:
                star_pairs[pair] = last_val
            
            # Prepare customdata for hovertemplate
            peak_marker = np.where(np.isclose(y, peak_val, atol=1e-8, equal_nan=True), "*", "")
            
            customdata_stack = [
                np.full_like(y, pair, dtype=object),
                np.full_like(y, color, dtype=object),
                peak_marker
            ]
            customdata_array = np.stack(customdata_stack, axis=-1) if len(y) else None
            
            # Add main line trace to appropriate subplot
            if has_adx_subplot:
                fig.add_trace(go.Scatter(
                    x=y.index,
                    y=y,
                    mode="lines",
                    name=f"{pair} {'★' if is_peak else ''}",
                    line=dict(color=color, width=2),
                    hovertemplate=(
                        "<span style='color:%{customdata[1]};'>●</span> "
                        "%{customdata[0]} "
                        "%{y:.4f}"
                        "%{customdata[2]}"
                        "<extra></extra>"
                    ),
                    customdata=customdata_array,
                ), row=1, col=1)
            else:
                fig.add_trace(go.Scatter(
                    x=y.index,
                    y=y,
                    mode="lines",
                    name=f"{pair} {'★' if is_peak else ''}",
                    line=dict(color=color, width=2),
                    hovertemplate=(
                        "<span style='color:%{customdata[1]};'>●</span> "
                        "%{customdata[0]} "
                        "%{y:.4f}"
                        "%{customdata[2]}"
                        "<extra></extra>"
                    ),
                    customdata=customdata_array,
                ))
            
            # Add peak marker
            if peak_idx is not None and not pd.isnull(peak_val):
                if has_adx_subplot:
                    fig.add_trace(go.Scatter(
                        x=[peak_idx],
                        y=[peak_val],
                        mode="markers",
                        marker=dict(color=color, size=10, symbol="star"),
                        name=f"{pair} Peak",
                        showlegend=False,
                        hovertemplate=(
                            "<b>Peak:</b> %{customdata[0]}<br>"
                            "Value: %{y:.4f}"
                            "<extra></extra>"
                        ),
                        customdata=np.array([[pair]]),
                    ), row=1, col=1)
                else:
                    fig.add_trace(go.Scatter(
                        x=[peak_idx],
                        y=[peak_val],
                        mode="markers",
                        marker=dict(color=color, size=10, symbol="star"),
                        name=f"{pair} Peak",
                        showlegend=False,
                        hovertemplate=(
                            "<b>Peak:</b> %{customdata[0]}<br>"
                            "Value: %{y:.4f}"
                            "<extra></extra>"
                        ),
                        customdata=np.array([[pair]]),
                    ))

        # Add ADX indicator if requested
        if show_adx:
            self._add_adx_indicator(fig, cumulative_returns_ts, rolling_dispersion_df, adx_window, has_adx_subplot)

        # Calculate x-axis range
        x_range = self._calculate_x_range(cumulative_returns_ts.index)
        
        # Update layout to match Matrix E settings
        if has_adx_subplot:
            # Subplot layout
            fig.update_layout(
                template="plotly_dark",
                legend=dict(
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01,
                    bgcolor="rgba(0,0,0,0.7)",  # More opaque background
                    bordercolor="rgba(255,255,255,0.3)",
                    borderwidth=1,
                    font=dict(size=10),  # Much smaller font
                    itemwidth=30,  # Minimum allowed value
                    itemsizing="constant",
                    tracegroupgap=0,  # No spacing between items
                    itemclick="toggle",
                    itemdoubleclick="toggleothers",
                    orientation="v",
                    traceorder="normal",
                    valign="top",
                    entrywidth=60,  # Much smaller entry width
                    entrywidthmode="pixels"
                ),
                hovermode="x unified",
                height=700,  # Taller for subplots
                width=1400,
                margin=dict(l=40, r=20, t=80, b=40),
                font=dict(size=14),
                autosize=False,
            )
            # Update y-axis titles for subplots
            fig.update_yaxes(title_text="Rolling CSSD Dispersion", row=1, col=1)
            fig.update_yaxes(title_text="ADX", row=2, col=1)
        else:
            # Single plot layout
            fig.update_layout(
                title="Rolling Dispersion of Normalized Returns CSSD",
                xaxis_title="Time",
                yaxis_title="Rolling CSSD Dispersion",
                template="plotly_dark",
                legend=dict(
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01,
                    bgcolor="rgba(0,0,0,0.7)",  # More opaque background
                    bordercolor="rgba(255,255,255,0.3)",
                    borderwidth=1,
                    font=dict(size=10),  # Much smaller font
                    itemwidth=30,  # Minimum allowed value
                    itemsizing="constant",
                    tracegroupgap=0,  # No spacing between items
                    itemclick="toggle",
                    itemdoubleclick="toggleothers",
                    orientation="v",
                    traceorder="normal",
                    valign="top",
                    entrywidth=60,  # Much smaller entry width
                    entrywidthmode="pixels"
                ),
                hovermode="x unified",
                height=600,
                width=1400,
                margin=dict(l=40, r=20, t=60, b=40),
                font=dict(size=14),
                xaxis_domain=[0, 1],
                autosize=False,
            )
        
        if x_range:
            fig.update_xaxes(range=x_range)
        
        # Format weight strings
        from dispersion_calculator import DispersionCalculator
        calc = DispersionCalculator()
        near_peak_weights_str = calc.format_weights_string(near_peak_pairs)
        star_weights_str = calc.format_weights_string(star_pairs)
        
        logger.info(f"Created rolling dispersion chart with {len(sorted_pairs_by_final_value)} pairs")
        
        return {
            'figure': fig,
            'near_peak_weights_str': near_peak_weights_str,
            'star_weights_str': star_weights_str
        }
    
    def create_dispersion_with_retracement_chart(self,
                                               dispersion_ts: pd.Series,
                                               retracement_pct: pd.Series,
                                               rolling_cssd_dispersion: pd.Series = None) -> go.Figure:
        """
        Create a chart showing dispersion with retracement analysis and rolling CSSD dispersion

        Args:
            dispersion_ts: Series with dispersion values
            retracement_pct: Series with retracement percentages
            rolling_cssd_dispersion: Series with rolling dispersion of CSSD values

        Returns:
            Plotly figure with subplots
        """
        if dispersion_ts.empty:
            logger.warning("Empty dispersion data provided for retracement chart")
            return go.Figure().update_layout(
                title="Dispersion with Retracement",
                template="plotly_dark"
            )

        # Create subplots: main chart and rolling CSSD dispersion subchart
        from plotly.subplots import make_subplots

        subplot_titles = ["Dispersion with Retracement", "Rolling Dispersion of CSSD"]
        if rolling_cssd_dispersion is None or rolling_cssd_dispersion.empty:
            # Single plot if no rolling CSSD data
            fig = go.Figure()
            has_subchart = False
        else:
            # Two subplots if rolling CSSD data is available
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=subplot_titles,
                vertical_spacing=0.08,
                row_heights=[0.8, 0.2],
                specs=[[{"secondary_y": True}], [{"secondary_y": False}]]
            )
            has_subchart = True
        
        # Add main dispersion line
        if has_subchart:
            fig.add_trace(go.Scatter(
                x=dispersion_ts.index,
                y=dispersion_ts,
                mode='lines',
                name='Dispersion',
                line=dict(color='white', width=2),
                hovertemplate='Dispersion: %{y:.4f}<extra></extra>'
            ), row=1, col=1)
        else:
            fig.add_trace(go.Scatter(
                x=dispersion_ts.index,
                y=dispersion_ts,
                mode='lines',
                name='Dispersion',
                line=dict(color='white', width=2),
                yaxis='y',
                hovertemplate='Dispersion: %{y:.4f}<extra></extra>'
            ))

        # Add colored retracement segments if retracement data is available
        if not retracement_pct.empty:
            self._add_retracement_segments(fig, retracement_pct, has_subchart)
            self._add_retracement_reference_lines(fig, has_subchart)

        # Add rolling CSSD dispersion subchart if available
        if has_subchart and rolling_cssd_dispersion is not None and not rolling_cssd_dispersion.empty:
            self._add_rolling_cssd_with_trend_coloring(fig, rolling_cssd_dispersion)
        
        # Calculate x-axis range
        x_range = self._calculate_x_range(dispersion_ts.index)
        
        # Update layout based on whether we have subcharts
        if has_subchart:
            # Subplot layout
            fig.update_layout(
                template="plotly_dark",
                height=700,  # Taller for subplots
                width=1400,
                margin=dict(l=40, r=20, t=80, b=40),
                font=dict(size=14),
                hovermode="x unified",
                autosize=False,
                legend=dict(
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01
                )
            )
            # Ensure x-axes use full width for subplots
            fig.update_xaxes(domain=[0, 1])
            # Update y-axes for subplots
            fig.update_yaxes(title_text="Dispersion", row=1, col=1, secondary_y=False)
            fig.update_yaxes(title_text="Rolling CSSD Dispersion", row=2, col=1)
            # Add secondary y-axis for retracement on first subplot
            if not retracement_pct.empty:
                fig.update_layout(
                    yaxis2=dict(
                        title=dict(text='Retracement (%)', font=dict(color='yellow')),
                        tickfont=dict(color='yellow'),
                        overlaying='y',
                        side='right',
                        position=0.85,
                        anchor='free',
                        tickvals=[0, 12.5, 25, 37.5, 50, 62.5, 75, 87.5, 100, retracement_pct.max()],
                        ticktext=['0', '12.5', '25', '37.5', '50', '62.5', '75', '87.5', f'{retracement_pct.max():.0f}'],
                        range=[0, retracement_pct.max() * 1.1]
                    )
                )
        else:
            # Single chart layout with dual y-axes
            fig.update_layout(
                title="Dispersion with Retracement",
                xaxis_title="Time",
                template="plotly_dark",
                height=600,
                width=1400,
                margin=dict(l=40, r=20, t=60, b=40),
                font=dict(size=14),
                hovermode="x unified",
                xaxis_domain=[0, 1],
                autosize=False,
                legend=dict(
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01
                ),
                yaxis=dict(
                    title="Dispersion",
                    side="left"
                ),
                yaxis2=dict(
                    title=dict(text='Retracement (%)', font=dict(color='yellow')),
                    tickfont=dict(color='yellow'),
                    overlaying='y',
                    side='right',
                    position=0.85,
                    anchor='free',
                    tickvals=[0, 12.5, 25, 37.5, 50, 62.5, 75, 87.5, 100, retracement_pct.max() if not retracement_pct.empty else 100],
                    ticktext=['0', '12.5', '25', '37.5', '50', '62.5', '75', '87.5', f'{retracement_pct.max():.0f}' if not retracement_pct.empty else '100'],
                    range=[0, retracement_pct.max() * 1.1] if not retracement_pct.empty else [0, 100]
                )
            )
        
        if x_range:
            fig.update_xaxes(range=x_range)
        
        logger.info("Created dispersion with retracement chart")
        return fig
    
    def _get_pair_color(self, pair: str, last_return_value: float) -> str:
        """Get color for a currency pair based on the last return value"""
        if len(pair) != 6:
            return CURRENCY_COLORS.get('USD', '#2ca02c')

        base_currency = pair[:3]
        quote_currency = pair[3:6]

        if last_return_value >= 0:
            return CURRENCY_COLORS.get(base_currency, CURRENCY_COLORS.get('USD', '#2ca02c'))
        else:
            return CURRENCY_COLORS.get(quote_currency, CURRENCY_COLORS.get('USD', '#2ca02c'))
    
    def _calculate_x_range(self, index: pd.DatetimeIndex) -> Optional[list]:
        """Calculate appropriate x-axis range for charts"""
        if len(index) == 0:
            return None
        
        first_ts = index[0]
        last_ts = index[-1]
        tz = getattr(first_ts, 'tz', None) or MARKET_TIMEZONE
        
        if hasattr(first_ts, "date"):
            start_of_day = tz.localize(datetime.combine(first_ts.date(), time.min), is_dst=None)
        else:
            start_of_day = index.min()
        
        return [start_of_day, last_ts + timedelta(minutes=1)]

    def _add_retracement_segments(self, fig: go.Figure, retracement_pct: pd.Series, has_subchart: bool = False):
        """Add colored retracement segments to the figure"""
        # Define colors and thresholds
        retr_colors = ['blue', 'green', 'yellow', 'orange', 'red', 'magenta']
        retr_thresholds = [0, 6.25, 12.5, 25, 37.5, 50]
        retr_labels = ['0-6.25%', '6.25-12.5%', '12.5-25%', '25-37.5%', '37.5-50%', '50%+']

        # Iterate through thresholds to create segments
        current_segment_x = []
        current_segment_y = []
        current_color_idx = -1

        for i in range(len(retracement_pct)):
            val = retracement_pct.iloc[i]
            color_idx = -1

            # Determine color index based on retracement value
            if val >= retr_thresholds[5]:
                color_idx = 5
            elif val >= retr_thresholds[4]:
                color_idx = 4
            elif val >= retr_thresholds[3]:
                color_idx = 3
            elif val >= retr_thresholds[2]:
                color_idx = 2
            elif val >= retr_thresholds[1]:
                color_idx = 1
            elif val >= retr_thresholds[0]:
                color_idx = 0

            if i > 0 and color_idx != current_color_idx:
                # End previous segment and start new one
                if current_segment_x:
                    # Add the connecting point to the previous segment
                    current_segment_x.append(retracement_pct.index[i])
                    current_segment_y.append(retracement_pct.iloc[i])
                    if has_subchart:
                        fig.add_trace(go.Scatter(
                            x=current_segment_x,
                            y=current_segment_y,
                            mode='lines',
                            line=dict(color=retr_colors[current_color_idx], dash='solid'),
                            showlegend=False,
                            hovertemplate=f'Retracement ({retr_labels[current_color_idx]}): %{{y:.2f}}%<extra></extra>'
                        ), row=1, col=1, secondary_y=True)
                    else:
                        fig.add_trace(go.Scatter(
                            x=current_segment_x,
                            y=current_segment_y,
                            mode='lines',
                            line=dict(color=retr_colors[current_color_idx], dash='solid'),
                            yaxis='y2',
                            showlegend=False,
                            hovertemplate=f'Retracement ({retr_labels[current_color_idx]}): %{{y:.2f}}%<extra></extra>'
                        ))
                # Start new segment
                current_segment_x = [retracement_pct.index[i-1], retracement_pct.index[i]]
                current_segment_y = [retracement_pct.iloc[i-1], retracement_pct.iloc[i]]
                current_color_idx = color_idx
            else:
                # Continue current segment
                if not current_segment_x:  # First point
                    current_segment_x.append(retracement_pct.index[i])
                    current_segment_y.append(retracement_pct.iloc[i])
                    current_color_idx = color_idx
                else:
                    current_segment_x.append(retracement_pct.index[i])
                    current_segment_y.append(retracement_pct.iloc[i])

        # Add the last segment
        if current_segment_x:
            if has_subchart:
                fig.add_trace(go.Scatter(
                    x=current_segment_x,
                    y=current_segment_y,
                    mode='lines',
                    line=dict(color=retr_colors[current_color_idx], dash='solid'),
                    showlegend=False,
                    hovertemplate=f'Retracement ({retr_labels[current_color_idx]}): %{{y:.2f}}%<extra></extra>'
                ), row=1, col=1, secondary_y=True)
            else:
                fig.add_trace(go.Scatter(
                    x=current_segment_x,
                    y=current_segment_y,
                    mode='lines',
                    line=dict(color=retr_colors[current_color_idx], dash='solid'),
                    yaxis='y2',
                    showlegend=False,
                    hovertemplate=f'Retracement ({retr_labels[current_color_idx]}): %{{y:.2f}}%<extra></extra>'
                ))

        # Add dummy traces for Retracement legend
        for i, label in enumerate(retr_labels):
            row_col = (1, 1) if has_subchart else (None, None)
            fig.add_trace(go.Scatter(
                x=[None], y=[None], mode='lines',
                line=dict(color=retr_colors[i], dash='solid'),
                name=f'Retracement {label}',
                legendgroup="retracement"
            ), row=row_col[0], col=row_col[1])

    def _add_retracement_reference_lines(self, fig: go.Figure, has_subchart: bool = False):
        """Add reference lines at specific retracement levels"""
        reference_levels = [
            (12.5, 'green'),
            (25, 'yellow'),
            (37.5, 'orange')
        ]

        for level, color in reference_levels:
            if has_subchart:
                # For subplots, add hline to specific row
                fig.add_hline(
                    y=level,
                    line=dict(color=color, width=2, dash='dot'),
                    opacity=1,
                    layer='below',
                    yref='y2',
                    annotation_text=f"{level}%",
                    annotation_position="left",
                    annotation_font=dict(color=color, size=10),
                    row=1, col=1
                )
            else:
                # For single plot
                fig.add_hline(
                    y=level,
                    line=dict(color=color, width=2, dash='dot'),
                    opacity=1,
                    layer='below',
                    yref='y2',
                    annotation_text=f"{level}%",
                    annotation_position="left",
                    annotation_font=dict(color=color, size=10)
                )

    def _add_rolling_cssd_with_trend_coloring(self, fig: go.Figure, rolling_cssd_dispersion: pd.Series):
        """
        Add rolling CSSD dispersion with trend change coloring

        Red: 2 consecutive down movements followed by 2 consecutive up movements (4 points total)
        Blue: 2 consecutive up movements followed by 2 consecutive down movements (4 points total)
        """
        if len(rolling_cssd_dispersion) < 4:
            # Not enough data for trend analysis, add simple line
            fig.add_trace(go.Scatter(
                x=rolling_cssd_dispersion.index,
                y=rolling_cssd_dispersion,
                mode='lines',
                name='Rolling CSSD Dispersion',
                line=dict(color='cyan', width=2),
                hovertemplate='Rolling CSSD Dispersion: %{y:.4f}<extra></extra>'
            ), row=2, col=1)
            return

        # Calculate trend changes
        values = rolling_cssd_dispersion.values
        timestamps = rolling_cssd_dispersion.index

        # Track segments with different colors
        segments = []
        current_segment = {'x': [], 'y': [], 'color': 'cyan'}

        # Identify trend change patterns
        red_indices = set()  # down-down-up-up patterns
        blue_indices = set()  # up-up-down-down patterns

        for i in range(len(values) - 3):  # Check each possible 4-point window
            # Check for down-down-up-up pattern (mark as red)
            if (values[i] > values[i+1] and      # down
                values[i+1] > values[i+2] and    # down
                values[i+2] < values[i+3]):      # up
                # Check if there's a 4th up movement (if next point exists)
                if i+4 < len(values) and values[i+3] < values[i+4]:
                    # Mark these 4 points as red
                    for j in range(i, i+4):
                        red_indices.add(j)

            # Check for up-up-down-down pattern (mark as blue)
            elif (values[i] < values[i+1] and    # up
                  values[i+1] < values[i+2] and  # up
                  values[i+2] > values[i+3]):    # down
                # Check if there's a 4th down movement (if next point exists)
                if i+4 < len(values) and values[i+3] > values[i+4]:
                    # Mark these 4 points as blue
                    for j in range(i, i+4):
                        blue_indices.add(j)

        # Create segments based on coloring
        i = 0
        while i < len(values):
            current_color = 'cyan'
            if i in red_indices:
                current_color = 'red'
            elif i in blue_indices:
                current_color = 'blue'

            if current_color != 'cyan':
                # End current segment if it has points
                if current_segment['x']:
                    segments.append(current_segment.copy())
                    current_segment = {'x': [], 'y': [], 'color': 'cyan'}

                # Create colored segment for trend change points
                trend_segment = {'x': [timestamps[i]], 'y': [values[i]], 'color': current_color}

                # Continue collecting consecutive trend change points with same color
                while i+1 < len(values) and ((i+1) in red_indices and current_color == 'red') or ((i+1) in blue_indices and current_color == 'blue'):
                    i += 1
                    trend_segment['x'].append(timestamps[i])
                    trend_segment['y'].append(values[i])

                segments.append(trend_segment)

            else:
                # Add to current normal segment
                current_segment['x'].append(timestamps[i])
                current_segment['y'].append(values[i])

            i += 1

        # Add final segment if it has points
        if current_segment['x']:
            segments.append(current_segment)

        # Add all segments to the plot
        for segment in segments:
            if len(segment['x']) > 0:
                fig.add_trace(go.Scatter(
                    x=segment['x'],
                    y=segment['y'],
                    mode='lines+markers' if segment['color'] != 'cyan' else 'lines',
                    name='Rolling CSSD Dispersion' if segment['color'] == 'cyan' else f'Trend Change ({segment["color"]})',
                    line=dict(color=segment['color'], width=3 if segment['color'] != 'cyan' else 2),
                    marker=dict(size=6) if segment['color'] != 'cyan' else None,
                    showlegend=False,  # Don't show legend for any segments to avoid clutter
                    hovertemplate=f'Rolling CSSD Dispersion: %{{y:.4f}}<extra></extra>'
                ), row=2, col=1)

    def _add_adx_indicator(self, fig: go.Figure, cumulative_returns_ts: pd.DataFrame, rolling_dispersion_df: pd.DataFrame, adx_window: int = 14, has_subplot: bool = False):
        """
        Add ADX indicator to the rolling dispersion chart
        Calculates ADX on the sum of all normalized log returns across all currency pairs

        Args:
            fig: Plotly figure to add ADX to
            cumulative_returns_ts: DataFrame with normalized log returns for all pairs
            rolling_dispersion_df: DataFrame with rolling dispersion values (for scaling if overlay)
            adx_window: Window size for ADX calculation
            has_subplot: Whether to add ADX as subplot (True) or overlay (False)
        """
        from utils import calculate_adx_from_returns

        # Sum all normalized log returns across all currency pairs
        if cumulative_returns_ts.empty:
            return

        # Calculate the sum of all pairs' normalized log returns
        summed_returns = cumulative_returns_ts.sum(axis=1)

        if len(summed_returns) < adx_window * 2:  # Need enough data for ADX calculation
            return

        # Calculate ADX on the summed returns
        adx_values = calculate_adx_from_returns(summed_returns, adx_window)

        if adx_values.empty or adx_values.isna().all():
            return

        if has_subplot:
            # Add ADX to its own subplot (no scaling needed)
            fig.add_trace(go.Scatter(
                x=adx_values.index,
                y=adx_values,
                mode='lines',
                name='ADX (Market Sum)',
                line=dict(
                    color='orange',
                    width=2
                ),
                opacity=0.8,
                hovertemplate=(
                    'Market ADX: %{y:.2f}<br>'
                    '<i>Based on sum of all 28 pairs</i>'
                    '<extra></extra>'
                ),
                showlegend=True
            ), row=2, col=1)

            # Add ADX reference lines to subplot
            for adx_level, color, name in [(25, 'yellow', 'ADX 25'), (50, 'red', 'ADX 50')]:
                fig.add_hline(
                    y=adx_level,
                    line=dict(color=color, width=1, dash='dashdot'),
                    opacity=0.5,
                    annotation_text=name,
                    annotation_position="right",
                    annotation_font=dict(color=color, size=10),
                    row=2, col=1
                )
        else:
            # Scale ADX to fit with dispersion values (ADX is 0-100, dispersion varies)
            dispersion_max = rolling_dispersion_df.max().max()
            dispersion_min = rolling_dispersion_df.min().min()

            # Scale ADX from 0-100 to dispersion range
            adx_scaled = (adx_values / 100) * (dispersion_max - dispersion_min) + dispersion_min

            # Add single ADX trace for the summed market data as overlay
            fig.add_trace(go.Scatter(
                x=adx_scaled.index,
                y=adx_scaled,
                mode='lines',
                name='ADX (Market Sum)',
                line=dict(
                    color='orange',
                    width=2,
                    dash='dot'
                ),
                opacity=0.8,
                hovertemplate=(
                    'Market ADX: %{customdata:.2f}<br>'
                    'Scaled: %{y:.4f}<br>'
                    '<i>Based on sum of all 28 pairs</i>'
                    '<extra></extra>'
                ),
                customdata=adx_values,  # Show original ADX values in hover
                visible='legendonly',  # Start hidden, user can toggle
                showlegend=True
            ))

            # Add ADX reference lines (scaled to dispersion range)
            for adx_level, color, name in [(25, 'yellow', 'ADX 25'), (50, 'red', 'ADX 50')]:
                scaled_level = (adx_level / 100) * (dispersion_max - dispersion_min) + dispersion_min

                fig.add_hline(
                    y=scaled_level,
                    line=dict(color=color, width=1, dash='dashdot'),
                    opacity=0.5,
                    annotation_text=name,
                    annotation_position="right",
                    annotation_font=dict(color=color, size=10),
                    visible=False  # Start hidden, will be visible when ADX trace is shown
                )

    def create_portfolio_entry_warning_chart(self,
                                            cssd_series: pd.Series,
                                            normalized_returns_ts: pd.DataFrame,
                                            lookback_window: int = 240,
                                            threshold_percentile: float = 95.0,
                                            roc_window: int = 15,
                                            roc_sigma_multiplier: float = 2.0,
                                            persistence_bars: int = 3,
                                            pre_quiet_window: int = 60,
                                            adx_window: int = 14,
                                            adx_threshold: float = 25.0,
                                            entry_window_minutes: int = 30) -> go.Figure:
        """
        Create a Portfolio Entry Warning System chart based on dispersion breakout strategy

        Implements the 6-step signal detection process:
        1. CSSD threshold ≥ T₁ (95th percentile of last N minutes)
        2. ROC filter ≥ T₂ (ΔCSSD ≥ 2σ within 15 minutes)
        3. Persistence (CSSD stays > T₁ for ≥ 3 bars)
        4. Pre-quiet check (Previous 60 minutes CSSD < 50th percentile)
        5. Trend confirmation (ADX > 25 and rising)
        6. Entry window (30-minute countdown after signal)

        Args:
            cssd_series: Series with CSSD dispersion values
            normalized_returns_ts: DataFrame with normalized returns for ADX calculation
            lookback_window: Window for threshold calculation (default 240 minutes)
            threshold_percentile: Percentile for T1 threshold (default 95.0)
            roc_window: Window for ROC calculation (default 15 minutes)
            roc_sigma_multiplier: Sigma multiplier for ROC filter (default 2.0)
            persistence_bars: Required bars above threshold (default 3)
            pre_quiet_window: Window for pre-quiet check (default 60 minutes)
            adx_window: Window for ADX calculation (default 14)
            adx_threshold: ADX threshold for trend confirmation (default 25.0)
            entry_window_minutes: Entry window duration (default 30 minutes)

        Returns:
            Plotly figure with warning system chart
        """
        if cssd_series.empty or normalized_returns_ts.empty:
            logger.warning("Empty data provided for portfolio entry warning chart")
            return go.Figure().update_layout(
                title="Portfolio Entry Warning System",
                template="plotly_dark"
            )

        # Calculate signal detection components
        signal_data = self._calculate_entry_signals(
            cssd_series, normalized_returns_ts, lookback_window, threshold_percentile,
            roc_window, roc_sigma_multiplier, persistence_bars, pre_quiet_window,
            adx_window, adx_threshold, entry_window_minutes
        )

        # Create subplots
        from plotly.subplots import make_subplots
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=[
                "CSSD with Dynamic Threshold & Entry Signals",
                "Rate of Change (ROC) with Sigma Bands",
                "ADX Trend Confirmation"
            ],
            vertical_spacing=0.08,
            row_heights=[0.5, 0.25, 0.25],
            specs=[[{"secondary_y": False}], [{"secondary_y": False}], [{"secondary_y": False}]]
        )

        # Add CSSD main chart with signals
        self._add_cssd_with_signals(fig, cssd_series, signal_data)

        # Add ROC chart
        self._add_roc_chart(fig, signal_data)

        # Add ADX chart
        self._add_adx_chart(fig, signal_data)

        # Update layout
        fig.update_layout(
            template="plotly_dark",
            height=900,
            width=1400,
            margin=dict(l=40, r=20, t=100, b=40),
            font=dict(size=12),
            hovermode="x unified",
            autosize=False,
            legend=dict(
                yanchor="top",
                y=0.99,
                xanchor="left",
                x=0.01,
                bgcolor="rgba(0,0,0,0.8)",
                bordercolor="rgba(255,255,255,0.3)",
                borderwidth=1,
                font=dict(size=10)
            ),
            title=dict(
                text="Portfolio Entry Warning System - Dispersion Breakout Strategy",
                x=0.5,
                font=dict(size=16)
            )
        )

        # Calculate x-axis range
        x_range = self._calculate_x_range(cssd_series.index)
        if x_range:
            fig.update_xaxes(range=x_range)

        # Update y-axis titles
        fig.update_yaxes(title_text="CSSD Dispersion", row=1, col=1)
        fig.update_yaxes(title_text="ROC (σ)", row=2, col=1)
        fig.update_yaxes(title_text="ADX", row=3, col=1)

        logger.info("Created portfolio entry warning system chart")
        return fig

    def _calculate_entry_signals(self, cssd_series: pd.Series, normalized_returns_ts: pd.DataFrame,
                               lookback_window: int, threshold_percentile: float, roc_window: int,
                               roc_sigma_multiplier: float, persistence_bars: int, pre_quiet_window: int,
                               adx_window: int, adx_threshold: float, entry_window_minutes: int) -> Dict:
        """
        Calculate all signal detection components for the warning system

        Returns:
            Dictionary containing all calculated signals and thresholds
        """
        from utils import calculate_adx_from_returns

        # 1. Calculate dynamic CSSD threshold (T1) - rolling 95th percentile
        threshold_t1 = cssd_series.rolling(window=lookback_window, min_periods=1).quantile(threshold_percentile / 100.0)

        # 2. Calculate Rate of Change (ROC) and sigma bands
        roc_series = cssd_series.diff(roc_window)  # Change over roc_window periods
        roc_mean = roc_series.rolling(window=lookback_window, min_periods=1).mean()
        roc_std = roc_series.rolling(window=lookback_window, min_periods=1).std()
        roc_upper_band = roc_mean + (roc_sigma_multiplier * roc_std)
        roc_lower_band = roc_mean - (roc_sigma_multiplier * roc_std)

        # 3. Calculate ADX from summed normalized returns
        summed_returns = normalized_returns_ts.sum(axis=1)
        adx_values = calculate_adx_from_returns(summed_returns, adx_window)
        adx_rising = adx_values.diff() > 0  # ADX is rising

        # 4. Calculate 50th percentile for pre-quiet check
        threshold_50th = cssd_series.rolling(window=lookback_window, min_periods=1).quantile(0.5)

        # 5. Detect signal conditions
        signals = self._detect_breakout_signals(
            cssd_series, threshold_t1, roc_series, roc_upper_band,
            adx_values, adx_threshold, adx_rising, threshold_50th,
            persistence_bars, pre_quiet_window, entry_window_minutes
        )

        return {
            'cssd': cssd_series,
            'threshold_t1': threshold_t1,
            'threshold_50th': threshold_50th,
            'roc_series': roc_series,
            'roc_mean': roc_mean,
            'roc_std': roc_std,
            'roc_upper_band': roc_upper_band,
            'roc_lower_band': roc_lower_band,
            'adx_values': adx_values,
            'adx_rising': adx_rising,
            'signals': signals
        }

    def _detect_breakout_signals(self, cssd_series: pd.Series, threshold_t1: pd.Series,
                               roc_series: pd.Series, roc_upper_band: pd.Series,
                               adx_values: pd.Series, adx_threshold: float, adx_rising: pd.Series,
                               threshold_50th: pd.Series, persistence_bars: int, pre_quiet_window: int,
                               entry_window_minutes: int) -> Dict:
        """
        Detect qualified dispersion breakout signals based on 6-step process

        Returns:
            Dictionary with signal timestamps and entry windows
        """
        signals = {
            'qualified_breakouts': [],
            'entry_windows': [],
            'condition_flags': pd.DataFrame(index=cssd_series.index)
        }

        # Initialize condition flags
        flags = signals['condition_flags']
        flags['cssd_above_t1'] = cssd_series >= threshold_t1
        flags['roc_spike'] = roc_series >= roc_upper_band
        flags['adx_confirm'] = (adx_values > adx_threshold) & adx_rising
        flags['pre_quiet'] = False
        flags['persistence'] = False
        flags['qualified_signal'] = False

        # Check pre-quiet condition (previous 60 minutes below 50th percentile)
        for i in range(pre_quiet_window, len(cssd_series)):
            pre_quiet_period = cssd_series.iloc[i-pre_quiet_window:i]
            pre_quiet_threshold = threshold_50th.iloc[i-pre_quiet_window:i]
            flags.iloc[i, flags.columns.get_loc('pre_quiet')] = (pre_quiet_period < pre_quiet_threshold).all()

        # Check persistence condition (CSSD stays above T1 for required bars)
        for i in range(persistence_bars-1, len(cssd_series)):
            persistence_period = flags['cssd_above_t1'].iloc[i-persistence_bars+1:i+1]
            flags.iloc[i, flags.columns.get_loc('persistence')] = persistence_period.all()

        # Identify qualified signals (all conditions met)
        flags['qualified_signal'] = (
            flags['cssd_above_t1'] &
            flags['roc_spike'] &
            flags['adx_confirm'] &
            flags['pre_quiet'] &
            flags['persistence']
        )

        # Find signal timestamps and create entry windows
        signal_times = flags[flags['qualified_signal']].index

        for signal_time in signal_times:
            # Avoid duplicate signals within entry window
            if not signals['qualified_breakouts'] or \
               (signal_time - signals['qualified_breakouts'][-1]).total_seconds() > entry_window_minutes * 60:

                signals['qualified_breakouts'].append(signal_time)

                # Create entry window (30 minutes after signal)
                entry_end = signal_time + timedelta(minutes=entry_window_minutes)
                signals['entry_windows'].append({
                    'start': signal_time,
                    'end': entry_end,
                    'cssd_value': cssd_series.loc[signal_time],
                    'adx_value': adx_values.loc[signal_time] if signal_time in adx_values.index else np.nan
                })

        return signals

    def _add_cssd_with_signals(self, fig: go.Figure, cssd_series: pd.Series, signal_data: Dict):
        """Add CSSD line with threshold lines and signal markers to the main subplot"""

        # Add main CSSD line
        fig.add_trace(go.Scatter(
            x=cssd_series.index,
            y=cssd_series,
            mode='lines',
            name='CSSD Dispersion',
            line=dict(color='white', width=2),
            hovertemplate='CSSD: %{y:.4f}<extra></extra>'
        ), row=1, col=1)

        # Add dynamic threshold lines
        fig.add_trace(go.Scatter(
            x=signal_data['threshold_t1'].index,
            y=signal_data['threshold_t1'],
            mode='lines',
            name='T1 Threshold (95th %ile)',
            line=dict(color='red', width=2, dash='dash'),
            hovertemplate='T1 Threshold: %{y:.4f}<extra></extra>'
        ), row=1, col=1)

        fig.add_trace(go.Scatter(
            x=signal_data['threshold_50th'].index,
            y=signal_data['threshold_50th'],
            mode='lines',
            name='50th Percentile',
            line=dict(color='yellow', width=1, dash='dot'),
            hovertemplate='50th Percentile: %{y:.4f}<extra></extra>'
        ), row=1, col=1)

        # Add qualified breakout signals
        for signal_time in signal_data['signals']['qualified_breakouts']:
            if signal_time in cssd_series.index:
                cssd_value = cssd_series.loc[signal_time]
                fig.add_trace(go.Scatter(
                    x=[signal_time],
                    y=[cssd_value],
                    mode='markers',
                    name='Entry Signal',
                    marker=dict(
                        color='lime',
                        size=15,
                        symbol='star',
                        line=dict(color='darkgreen', width=2)
                    ),
                    showlegend=False,
                    hovertemplate='<b>ENTRY SIGNAL</b><br>Time: %{x}<br>CSSD: %{y:.4f}<extra></extra>'
                ), row=1, col=1)

        # Add entry windows as colored background regions
        for window in signal_data['signals']['entry_windows']:
            fig.add_vrect(
                x0=window['start'],
                x1=window['end'],
                fillcolor='rgba(0, 255, 0, 0.1)',
                layer='below',
                line_width=0,
                annotation_text=f"Entry Window<br>CSSD: {window['cssd_value']:.4f}",
                annotation_position="top left",
                annotation_font=dict(color='lime', size=10),
                row=1, col=1
            )

    def _add_roc_chart(self, fig: go.Figure, signal_data: Dict):
        """Add Rate of Change chart with sigma bands"""

        # Add ROC line
        fig.add_trace(go.Scatter(
            x=signal_data['roc_series'].index,
            y=signal_data['roc_series'],
            mode='lines',
            name='ROC (15min)',
            line=dict(color='cyan', width=2),
            hovertemplate='ROC: %{y:.4f}<extra></extra>'
        ), row=2, col=1)

        # Add sigma bands
        fig.add_trace(go.Scatter(
            x=signal_data['roc_upper_band'].index,
            y=signal_data['roc_upper_band'],
            mode='lines',
            name='+2σ Band',
            line=dict(color='red', width=1, dash='dash'),
            hovertemplate='+2σ: %{y:.4f}<extra></extra>'
        ), row=2, col=1)

        fig.add_trace(go.Scatter(
            x=signal_data['roc_lower_band'].index,
            y=signal_data['roc_lower_band'],
            mode='lines',
            name='-2σ Band',
            line=dict(color='red', width=1, dash='dash'),
            hovertemplate='-2σ: %{y:.4f}<extra></extra>'
        ), row=2, col=1)

        # Add mean line
        fig.add_trace(go.Scatter(
            x=signal_data['roc_mean'].index,
            y=signal_data['roc_mean'],
            mode='lines',
            name='ROC Mean',
            line=dict(color='gray', width=1, dash='dot'),
            hovertemplate='ROC Mean: %{y:.4f}<extra></extra>'
        ), row=2, col=1)

        # Highlight ROC spikes
        roc_spikes = signal_data['roc_series'] >= signal_data['roc_upper_band']
        spike_times = signal_data['roc_series'][roc_spikes].index
        spike_values = signal_data['roc_series'][roc_spikes].values

        if len(spike_times) > 0:
            fig.add_trace(go.Scatter(
                x=spike_times,
                y=spike_values,
                mode='markers',
                name='ROC Spikes',
                marker=dict(color='orange', size=8, symbol='triangle-up'),
                showlegend=False,
                hovertemplate='ROC Spike: %{y:.4f}<extra></extra>'
            ), row=2, col=1)

    def _add_adx_chart(self, fig: go.Figure, signal_data: Dict):
        """Add ADX chart with trend confirmation indicators"""

        # Add ADX line
        fig.add_trace(go.Scatter(
            x=signal_data['adx_values'].index,
            y=signal_data['adx_values'],
            mode='lines',
            name='ADX (Market Sum)',
            line=dict(color='orange', width=2),
            hovertemplate='ADX: %{y:.2f}<extra></extra>'
        ), row=3, col=1)

        # Add ADX threshold line
        fig.add_hline(
            y=25,
            line=dict(color='yellow', width=2, dash='dash'),
            annotation_text="ADX 25 (Trend Threshold)",
            annotation_position="right",
            annotation_font=dict(color='yellow', size=10),
            row=3, col=1
        )

        # Add ADX 50 reference line
        fig.add_hline(
            y=50,
            line=dict(color='red', width=1, dash='dot'),
            annotation_text="ADX 50 (Strong Trend)",
            annotation_position="right",
            annotation_font=dict(color='red', size=10),
            row=3, col=1
        )

        # Highlight ADX rising periods above threshold
        adx_confirm = (signal_data['adx_values'] > 25) & signal_data['adx_rising']
        confirm_times = signal_data['adx_values'][adx_confirm].index
        confirm_values = signal_data['adx_values'][adx_confirm].values

        if len(confirm_times) > 0:
            fig.add_trace(go.Scatter(
                x=confirm_times,
                y=confirm_values,
                mode='markers',
                name='ADX Confirmation',
                marker=dict(color='lime', size=6, symbol='circle'),
                showlegend=False,
                hovertemplate='ADX Confirm: %{y:.2f}<extra></extra>'
            ), row=3, col=1)


