# Matrix QP - Quantitative Portfolio Optimization

A sophisticated portfolio optimization application built on the foundation of matrix_mpt, designed for real-time forex portfolio management with advanced risk metrics and visualization.

## Overview

Matrix QP provides intelligent portfolio optimization for 28 major currency pairs using modern portfolio theory with enhanced risk management features. The application focuses on intraday optimization with start-of-day data collection and real-time risk assessment.

## Key Features

### Portfolio Optimization
- **Multi-objective optimization**: Sharpe ratio, Sortino ratio, and minimum variance
- **6-pair portfolio constraint**: Optimal diversification with manageable complexity
- **Currency distribution rules**: Maximum 2 occurrences per base/quote currency
- **Absolute weight normalization**: Total absolute weights sum to 1.0 for easy lot sizing

### Risk Management
- **Intraday risk profiling**: Drawdown and variance calculation since market open (00:00)
- **Negative weight handling**: Automatic short position indication for negative log returns
- **Real-time risk metrics**: Continuous monitoring of portfolio risk exposure

### Data Management
- **MT5 integration**: Direct connection to MetaTrader 5 for live market data
- **28 currency pairs**: Complete major forex coverage
- **Start-of-day collection**: Fresh data from 00:00 for daily optimization
- **Log returns calculation**: Proper statistical handling of price movements

### Visualization
- **Interactive dashboards**: Dash/Plotly-based web interface
- **Portfolio comparison**: Side-by-side analysis of optimization strategies
- **Risk visualization**: Graphical representation of risk metrics and distributions
- **Weight allocation**: Clear display of position sizing and direction

## Architecture

The system follows a modular architecture optimized for portfolio analysis and real-time optimization:

```
matrix_QP/
├── app.py                    # Main application entry point
├── config.py                 # Configuration and constants
├── mt5_connector.py          # MT5 connection and data fetching
├── log_returns.py            # Log returns calculation module
├── risk_calculator.py        # Risk profiling and metrics
├── portfolio_optimizer.py    # Core optimization engine
├── weight_manager.py         # Weight calculation and normalization
├── dashboard.py              # Dash/Plotly visualization
├── portfolio_presenter.py    # Portfolio presentation logic
├── utils.py                  # Utility functions
└── requirements.txt          # Python dependencies
```

## Currency Pairs

The application optimizes portfolios using 28 major forex pairs:

**EUR pairs**: EURUSD, EURGBP, EURAUD, EURNZD, EURCHF, EURCAD, EURJPY
**GBP pairs**: GBPUSD, GBPAUD, GBPNZD, GBPCHF, GBPCAD, GBPJPY
**AUD pairs**: AUDUSD, AUDNZD, AUDCHF, AUDCAD, AUDJPY
**NZD pairs**: NZDUSD, NZDCHF, NZDCAD, NZDJPY
**USD pairs**: USDCHF, USDCAD, USDJPY
**Cross pairs**: CADCHF, CADJPY, CHFJPY

## Portfolio Constraints

### Size Constraints
- **Fixed portfolio size**: Exactly 6 currency pairs per portfolio
- **Minimum portfolios**: At least 3 optimized portfolios presented
- **Optimization targets**: Sharpe, Sortino, and minimum variance strategies

### Currency Distribution Rules
- **Base currency limit**: Maximum 2 pairs with same base currency (e.g., XXXNZD)
- **Quote currency limit**: Maximum 2 pairs with same quote currency (e.g., NZDXXX)
- **Diversification enforcement**: Prevents over-concentration in single currencies

### Weight Management
- **Absolute normalization**: |w1| + |w2| + ... + |w6| = 1.0
- **Direction indication**: Negative weights indicate short positions
- **Lot size compatibility**: Direct multiplication with desired total lot size

## Usage Examples

### Web Dashboard
The easiest way to use Matrix QP is through the web dashboard:

```bash
python run.py dashboard
```

Then open your browser to `http://127.0.0.1:8050`

### Command Line Optimization
For automated trading systems, use the command line interface:

```bash
# Single optimization cycle
python run.py optimize

# Or with custom parameters
python app.py --mode optimize --lot-size 10.0
```

### Programmatic Usage
For integration into existing systems:

```python
import asyncio
from matrix_QP.app import MatrixQPApplication

# Create application instance
app = MatrixQPApplication()

# Initialize and run optimization
async def run_optimization():
    if await app.initialize():
        result = await app.run_optimization_cycle(total_lot_size=5.0)

        if result['status'] == 'success':
            print(f"Optimized {result['optimized_portfolios']} portfolios")
            print(f"Best Sharpe ratio: {result['summary']['best_sharpe']:.3f}")

        app.shutdown()

# Run the optimization
asyncio.run(run_optimization())
```

### Advanced Usage
For custom optimization strategies:

```python
from matrix_QP.mt5_connector import MT5Connector
from matrix_QP.log_returns import LogReturnsCalculator
from matrix_QP.portfolio_optimizer import PortfolioOptimizer
from matrix_QP.weight_manager import WeightManager

# Initialize components
connector = MT5Connector()
returns_calc = LogReturnsCalculator()
optimizer = PortfolioOptimizer()
weight_mgr = WeightManager()

# Connect and fetch data
with connector:
    data = connector.fetch_daily_data()

    # Calculate returns
    log_returns = returns_calc.calculate_log_returns(data)

    # Optimize portfolios
    portfolios = optimizer.optimize_portfolios(log_returns)

    # Process weights
    for portfolio in portfolios:
        weights = portfolio.get_weight_dict()
        directional_weights = weight_mgr.assign_weight_directions(weights, log_returns)
        lot_sizes = weight_mgr.calculate_lot_sizes(directional_weights, 10.0)

        print(f"Strategy: {portfolio.strategy}")
        print(f"Pairs: {portfolio.pairs}")
        print(f"Lot sizes: {lot_sizes}")
```

## Installation

### Prerequisites
- Python 3.8 or higher
- MetaTrader 5 terminal (for live data)
- Windows OS (required for MT5 integration)

### Quick Start
1. **Clone or copy the matrix_QP directory**
2. **Install dependencies:**
   ```bash
   cd matrix_QP
   pip install -r requirements.txt
   ```
3. **Check system requirements:**
   ```bash
   python run.py check
   ```
4. **Run tests:**
   ```bash
   python run.py test
   ```
5. **Start the dashboard:**
   ```bash
   python run.py dashboard
   ```

### Alternative Installation Methods

#### Using the launcher script:
```bash
# Check dependencies
python run.py check

# Run tests
python run.py test

# Single optimization
python run.py optimize

# Start dashboard
python run.py dashboard

# Check status
python run.py status
```

#### Direct application usage:
```bash
# Dashboard mode (default)
python app.py --mode dashboard --host 127.0.0.1 --port 8050

# Single optimization
python app.py --mode optimize --lot-size 1.0

# Status check
python app.py --mode status
```

## Dependencies

- MetaTrader5: Market data connection
- pandas: Data manipulation
- numpy: Numerical computations
- scipy: Optimization algorithms
- dash: Web interface
- plotly: Interactive visualizations
- numba: Performance optimization

## License

This project builds upon the matrix_mpt foundation and maintains compatibility with existing forex trading infrastructure.
