# ADX Indicator for Rolling Dispersion Chart

## Overview

The ADX (Average Directional Index) indicator has been added to the Rolling Dispersion of Normalized Returns CSSD chart as an optional overlay. This technical indicator helps analyze the strength of trends in the dispersion data.

## Features

### What is ADX?
- **ADX** measures the strength of a trend, regardless of direction
- Values range from 0 to 100
- **ADX < 25**: Weak or no trend
- **ADX 25-50**: Moderate trend strength  
- **ADX > 50**: Strong trend

### Implementation Details
- ADX is calculated on the **sum of all 28 currency pairs' normalized log returns**
- This provides a single ADX line representing overall market trend strength
- The indicator uses a simplified approach suitable for single-series data
- ADX values are scaled to match the dispersion data range for proper visualization
- Reference lines at ADX 25 and 50 levels are included (when visible)

## How to Use

### Enabling ADX
1. Navigate to the "Rolling Dispersion of Normalized Returns CSSD" chart section
2. Check the **"Show ADX Indicator"** checkbox
3. Optionally adjust the **ADX Window** parameter (default: 14)

### Controls
- **Show ADX Indicator**: Toggle checkbox to enable/disable ADX overlay
- **ADX Window**: Numeric input (5-50) to control the calculation period
  - Smaller values (5-10): More responsive, noisier
  - Larger values (20-50): Smoother, less responsive
  - Default (14): Standard ADX period

### Visual Elements
- **ADX Subplot**: Dedicated subplot below the main dispersion chart
- **ADX Line**: Single orange line representing market-wide trend strength
- **Proper Scaling**: ADX displayed in its natural 0-100 range (no scaling needed)
- **Reference Lines**: Yellow (ADX 25) and Red (ADX 50) horizontal lines in the ADX subplot
- **Chart Layout**: Main chart (75% height) + ADX subplot (25% height)

## Interpretation

### ADX in Context of Market Sum
- **Rising ADX**: Overall market trend is strengthening (all pairs moving in similar direction)
- **Falling ADX**: Market trend is weakening (pairs moving in different directions)
- **High ADX (>50)**: Strong directional movement across all currency pairs
- **Low ADX (<25)**: Choppy or sideways movement across the market

### Trading Insights
- **Strong ADX + Rising Market Sum**: Strong bullish trend across all pairs
- **Strong ADX + Falling Market Sum**: Strong bearish trend across all pairs
- **Weak ADX**: Market in consolidation, pairs moving independently
- **ADX vs Dispersion**: Compare ADX trend strength with dispersion levels for market regime analysis

## Technical Implementation

### Files Modified
- `utils.py`: Added `calculate_adx()` and `calculate_adx_from_returns()` functions
- `dispersion_charts.py`: Added `_add_adx_indicator()` method and ADX parameters
- `dashboard.py`: Added UI controls and callback integration

### ADX Calculation Method
```python
# Step 1: Sum all normalized log returns across 28 pairs
summed_returns = cumulative_returns_ts.sum(axis=1)

# Step 2: Calculate ADX on the summed series
def calculate_adx_from_returns(data: pd.Series, window: int = 14) -> pd.Series:
    # Creates synthetic OHLC from single series
    # Calculates True Range and Directional Movement
    # Returns smoothed ADX values
```

### Subplot Layout
ADX is displayed in its own dedicated subplot with proper scaling:
```python
# ADX subplot configuration
rows=2, cols=1,
subplot_titles=["Rolling Dispersion of Normalized Returns CSSD", "ADX (Market Sum)"],
row_heights=[0.75, 0.25]  # 75% main chart, 25% ADX
```

## Removal/Customization

### To Remove ADX
1. Uncheck the "Show ADX Indicator" checkbox
2. Chart will revert to single plot layout (no subplot)
3. Page refresh will completely remove ADX calculations

### To Customize
- Modify `adx_window` parameter in the UI (5-50 range)
- ADX reference lines (25, 50) are always visible when ADX is enabled
- ADX subplot height is fixed at 25% of total chart height

## Performance Notes

- ADX calculation adds minimal overhead
- Subplot layout provides clear separation between dispersion and ADX data
- No scaling needed - ADX displayed in natural 0-100 range
- Reference lines provide immediate context for ADX strength levels
- Chart height increases to 700px when ADX subplot is active

## Troubleshooting

### ADX Not Showing
- Ensure "Show ADX Indicator" is checked
- Check that there's sufficient data across all 28 pairs (minimum 2x window size)
- ADX appears in its own subplot below the main chart

### ADX Values Seem Wrong
- ADX is now displayed in its natural 0-100 range (no scaling)
- Verify ADX window setting is appropriate for your analysis timeframe
- ADX calculation requires sufficient historical data across all pairs to be meaningful
- The ADX reflects the trend strength of the **summed market movement**, not individual pairs

## Future Enhancements

Potential improvements could include:
- Additional technical indicators (RSI, MACD, etc.)
- Customizable ADX reference levels
- Color-coded ADX strength zones
- ADX divergence detection
- Export functionality for ADX data
