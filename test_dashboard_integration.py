"""
Test script to verify the Portfolio Entry Warning System integration with the dashboard
"""

import sys
import os

def test_imports():
    """Test that all required modules can be imported"""
    try:
        print("Testing imports...")
        
        # Test dispersion modules
        from dispersion_calculator import DispersionCalculator
        from dispersion_charts import DispersionChartCreator
        print("✅ Dispersion modules imported successfully")
        
        # Test dashboard module
        from dashboard import PortfolioDashboard
        print("✅ Dashboard module imported successfully")
        
        # Test that the warning system method exists
        chart_creator = DispersionChartCreator()
        if hasattr(chart_creator, 'create_portfolio_entry_warning_chart'):
            print("✅ Warning system chart method exists")
        else:
            print("❌ Warning system chart method not found")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_dashboard_layout():
    """Test that the dashboard layout includes the warning system components"""
    try:
        print("\nTesting dashboard layout...")
        
        from dashboard import PortfolioDashboard
        dashboard = PortfolioDashboard()
        
        # Check if the layout contains the warning system components
        layout_str = str(dashboard.app.layout)
        
        required_components = [
            'portfolio-warning-system-chart',
            'warning-system-status',
            'update-warning-system-btn',
            'warning-lookback-window',
            'warning-threshold-percentile',
            'warning-roc-sigma',
            'warning-adx-threshold'
        ]
        
        missing_components = []
        for component in required_components:
            if component not in layout_str:
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ Missing components: {missing_components}")
            return False
        else:
            print("✅ All warning system components found in layout")
            return True
            
    except Exception as e:
        print(f"❌ Error testing dashboard layout: {e}")
        return False

def test_callback_registration():
    """Test that the warning system callback is properly registered"""
    try:
        print("\nTesting callback registration...")
        
        from dashboard import PortfolioDashboard
        dashboard = PortfolioDashboard()
        
        # Check if the callback is registered
        callbacks = dashboard.app.callback_map
        
        # Look for the warning system callback
        warning_callback_found = False
        for callback_id, callback_info in callbacks.items():
            if 'portfolio-warning-system-chart' in str(callback_info.get('outputs', [])):
                warning_callback_found = True
                break
        
        if warning_callback_found:
            print("✅ Warning system callback registered successfully")
            return True
        else:
            print("❌ Warning system callback not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing callback registration: {e}")
        return False

def test_chart_creation():
    """Test that the warning system chart can be created with sample data"""
    try:
        print("\nTesting chart creation...")
        
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        from dispersion_charts import DispersionChartCreator
        
        # Create sample data
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=4)
        time_index = pd.date_range(start=start_time, end=end_time, freq='1min')
        
        # Sample CSSD data
        np.random.seed(42)
        cssd_values = np.random.normal(0.05, 0.01, len(time_index))
        cssd_series = pd.Series(cssd_values, index=time_index, name='CSSD')
        
        # Sample normalized returns for 28 pairs
        currency_pairs = [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDCHF', 'USDJPY',
            'EURGBP', 'EURAUD', 'EURNZD', 'EURCAD', 'EURCHF', 'EURJPY',
            'GBPAUD', 'GBPNZD', 'GBPCAD', 'GBPCHF', 'GBPJPY',
            'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY',
            'NZDCAD', 'NZDCHF', 'NZDJPY',
            'CADCHF', 'CADJPY',
            'CHFJPY'
        ]
        
        normalized_returns = {}
        for pair in currency_pairs:
            normalized_returns[pair] = np.random.normal(0, 0.001, len(time_index))
        
        normalized_returns_df = pd.DataFrame(normalized_returns, index=time_index)
        
        # Create chart
        chart_creator = DispersionChartCreator()
        fig = chart_creator.create_portfolio_entry_warning_chart(
            cssd_series=cssd_series,
            normalized_returns_ts=normalized_returns_df
        )
        
        if fig and hasattr(fig, 'data'):
            print("✅ Warning system chart created successfully")
            print(f"   Chart has {len(fig.data)} traces")
            return True
        else:
            print("❌ Failed to create warning system chart")
            return False
            
    except Exception as e:
        print(f"❌ Error testing chart creation: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Portfolio Entry Warning System Integration Test ===\n")
    
    tests = [
        test_imports,
        test_dashboard_layout,
        test_callback_registration,
        test_chart_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The warning system is successfully integrated.")
        print("\nTo use the warning system:")
        print("1. Run the dashboard: python dashboard.py")
        print("2. Navigate to the bottom of the page")
        print("3. Look for 'Portfolio Entry Warning System' section")
        print("4. Adjust parameters and click 'Update Warning System'")
    else:
        print("❌ Some tests failed. Please check the integration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
