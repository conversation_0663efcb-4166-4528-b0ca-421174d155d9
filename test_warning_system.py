"""
Test script for the Portfolio Entry Warning System chart
Demonstrates the new warning system functionality
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz
from dispersion_charts import DispersionChartCreator

def generate_sample_data():
    """Generate sample CSSD and normalized returns data for testing"""
    
    # Create sample time index (last 8 hours of minute data)
    end_time = datetime.now(pytz.timezone('Europe/Bucharest'))
    start_time = end_time - timedelta(hours=8)
    time_index = pd.date_range(start=start_time, end=end_time, freq='1min')
    
    # Generate sample CSSD data with some breakout patterns
    np.random.seed(42)  # For reproducible results
    
    # Base CSSD with trend and volatility
    base_trend = np.linspace(0.02, 0.08, len(time_index))
    noise = np.random.normal(0, 0.01, len(time_index))
    
    # Add some breakout spikes
    cssd_values = base_trend + noise
    
    # Add artificial breakout events
    breakout_indices = [100, 250, 400]  # Simulate breakouts at these indices
    for idx in breakout_indices:
        if idx < len(cssd_values):
            # Create a spike pattern
            spike_duration = 20  # 20 minutes
            spike_magnitude = 0.04
            for i in range(spike_duration):
                if idx + i < len(cssd_values):
                    cssd_values[idx + i] += spike_magnitude * np.exp(-i/10)  # Exponential decay
    
    cssd_series = pd.Series(cssd_values, index=time_index, name='CSSD')
    
    # Generate sample normalized returns for 28 currency pairs
    currency_pairs = [
        'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDCHF', 'USDJPY',
        'EURGBP', 'EURAUD', 'EURNZD', 'EURCAD', 'EURCHF', 'EURJPY',
        'GBPAUD', 'GBPNZD', 'GBPCAD', 'GBPCHF', 'GBPJPY',
        'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY',
        'NZDCAD', 'NZDCHF', 'NZDJPY',
        'CADCHF', 'CADJPY',
        'CHFJPY'
    ]
    
    # Generate correlated returns data
    normalized_returns = {}
    for pair in currency_pairs:
        # Create some correlation with CSSD spikes
        base_returns = np.random.normal(0, 0.001, len(time_index))
        
        # Add correlation with breakouts
        for idx in breakout_indices:
            if idx < len(base_returns):
                correlation_factor = np.random.choice([-1, 1])  # Random direction
                for i in range(20):
                    if idx + i < len(base_returns):
                        base_returns[idx + i] += correlation_factor * 0.002 * np.exp(-i/5)
        
        # Cumulative returns
        cumulative_returns = np.cumsum(base_returns)
        normalized_returns[pair] = cumulative_returns
    
    normalized_returns_df = pd.DataFrame(normalized_returns, index=time_index)
    
    return cssd_series, normalized_returns_df

def test_warning_system():
    """Test the Portfolio Entry Warning System chart"""
    
    print("Generating sample data...")
    cssd_series, normalized_returns_df = generate_sample_data()
    
    print("Creating Portfolio Entry Warning System chart...")
    chart_creator = DispersionChartCreator()
    
    # Create the warning system chart
    fig = chart_creator.create_portfolio_entry_warning_chart(
        cssd_series=cssd_series,
        normalized_returns_ts=normalized_returns_df,
        lookback_window=120,  # 2 hours for testing
        threshold_percentile=95.0,
        roc_window=15,
        roc_sigma_multiplier=2.0,
        persistence_bars=3,
        pre_quiet_window=30,  # 30 minutes for testing
        adx_window=14,
        adx_threshold=25.0,
        entry_window_minutes=30
    )
    
    print("Chart created successfully!")
    print(f"Data points: {len(cssd_series)}")
    print(f"Time range: {cssd_series.index[0]} to {cssd_series.index[-1]}")
    print(f"CSSD range: {cssd_series.min():.4f} to {cssd_series.max():.4f}")
    
    # Save the chart as HTML for viewing
    fig.write_html("portfolio_entry_warning_system.html")
    print("Chart saved as 'portfolio_entry_warning_system.html'")
    
    return fig

if __name__ == "__main__":
    test_warning_system()
