#!/usr/bin/env python3
"""
Matrix QP - Quick Start Script
Simple launcher for the Matrix QP portfolio optimization application
"""

import sys
import os
import subprocess
import argparse

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'MetaTrader5',
        'pandas',
        'numpy',
        'scipy',
        'dash',
        'plotly',
        'numba'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required dependencies are installed")
    return True

def run_tests():
    """Run the test suite"""
    print("🧪 Running Matrix QP test suite...")
    try:
        result = subprocess.run([sys.executable, 'test_application.py'], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def run_optimization():
    """Run a single optimization cycle"""
    print("📊 Running portfolio optimization...")
    try:
        result = subprocess.run([sys.executable, 'app.py', '--mode', 'optimize'], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Failed to run optimization: {e}")
        return False

def run_dashboard(host='127.0.0.1', port=8050):
    """Run the web dashboard"""
    print(f"🌐 Starting Matrix QP dashboard on http://{host}:{port}")
    print("Press Ctrl+C to stop the server")
    
    try:
        subprocess.run([sys.executable, 'app.py', '--mode', 'dashboard', 
                       '--host', host, '--port', str(port)])
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Failed to run dashboard: {e}")

def show_status():
    """Show application status"""
    print("📋 Checking Matrix QP status...")
    try:
        result = subprocess.run([sys.executable, 'app.py', '--mode', 'status'], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Failed to check status: {e}")
        return False

def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(description='Matrix QP - Quick Start Launcher')
    parser.add_argument('command', choices=['test', 'optimize', 'dashboard', 'status', 'check'],
                       help='Command to run')
    parser.add_argument('--host', default='127.0.0.1', help='Dashboard host (default: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=8050, help='Dashboard port (default: 8050)')
    
    args = parser.parse_args()
    
    print("🚀 Matrix QP - Quantitative Portfolio Optimization")
    print("=" * 50)
    
    # Change to the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    if args.command == 'check':
        print("🔍 Checking system requirements...")
        if check_dependencies():
            print("✅ System is ready for Matrix QP")
        else:
            print("❌ System requirements not met")
            sys.exit(1)
    
    elif args.command == 'test':
        if not check_dependencies():
            sys.exit(1)
        
        if run_tests():
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed")
            sys.exit(1)
    
    elif args.command == 'optimize':
        if not check_dependencies():
            sys.exit(1)
        
        if run_optimization():
            print("✅ Optimization completed successfully!")
        else:
            print("❌ Optimization failed")
            sys.exit(1)
    
    elif args.command == 'dashboard':
        if not check_dependencies():
            sys.exit(1)
        
        run_dashboard(args.host, args.port)
    
    elif args.command == 'status':
        if not check_dependencies():
            sys.exit(1)
        
        if show_status():
            print("✅ Status check completed")
        else:
            print("❌ Status check failed")
            sys.exit(1)

if __name__ == "__main__":
    main()
