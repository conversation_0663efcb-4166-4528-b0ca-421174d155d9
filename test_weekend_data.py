"""
Test script to verify weekend data fallback for Portfolio Entry Warning System
"""

import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_weekend_data_fallback():
    """Test the weekend data fallback logic"""
    try:
        from mt5_connector import MT5Connector
        
        logger.info("Testing weekend data fallback...")
        mt5_connector = MT5Connector()
        
        # Test 1: Current day only (should fail on weekend)
        logger.info("1. Testing current day only...")
        market_data = mt5_connector.fetch_daily_data(current_day_only=True)
        if market_data:
            logger.info(f"✅ Current day data available: {len(market_data)} pairs")
            return True, "today"
        else:
            logger.info("❌ No current day data available")
        
        # Test 2: Last 24 hours
        logger.info("2. Testing last 24 hours...")
        market_data = mt5_connector.fetch_daily_data(time_period='24h')
        if market_data:
            logger.info(f"✅ Last 24h data available: {len(market_data)} pairs")
            return True, "last 24h"
        else:
            logger.info("❌ No 24h data available")
        
        # Test 3: Last 48 hours (should include Friday)
        logger.info("3. Testing last 48 hours...")
        market_data = mt5_connector.fetch_daily_data(time_period='48h')
        if market_data:
            logger.info(f"✅ Last 48h data available: {len(market_data)} pairs")
            
            # Check data quality
            for symbol, data in list(market_data.items())[:3]:
                if hasattr(data, 'index') and len(data) > 0:
                    logger.info(f"{symbol}: {len(data)} rows, from {data.index[0]} to {data.index[-1]}")
            
            return True, "last 48h"
        else:
            logger.info("❌ No 48h data available")
        
        # Test 4: Last 72 hours (extended fallback)
        logger.info("4. Testing last 72 hours...")
        market_data = mt5_connector.fetch_daily_data(time_period='72h')
        if market_data:
            logger.info(f"✅ Last 72h data available: {len(market_data)} pairs")
            return True, "last 72h"
        else:
            logger.info("❌ No 72h data available")
        
        return False, None
        
    except Exception as e:
        logger.error(f"Error testing weekend data fallback: {e}")
        return False, None

def test_warning_system_with_fallback():
    """Test the complete warning system with fallback data"""
    try:
        from mt5_connector import MT5Connector
        from log_returns import LogReturnsCalculator
        from dispersion_calculator import DispersionCalculator
        from dispersion_charts import DispersionChartCreator
        
        logger.info("Testing complete warning system with fallback...")
        
        # Initialize components
        mt5_connector = MT5Connector()
        returns_calculator = LogReturnsCalculator()
        dispersion_calculator = DispersionCalculator()
        chart_creator = DispersionChartCreator()
        
        # Try data fallback logic
        market_data = mt5_connector.fetch_daily_data(current_day_only=True)
        data_source = "today"
        
        if not market_data:
            logger.info("Trying 24h fallback...")
            market_data = mt5_connector.fetch_daily_data(time_period='24h')
            data_source = "last 24h"
            
            if not market_data:
                logger.info("Trying 48h fallback...")
                market_data = mt5_connector.fetch_daily_data(time_period='48h')
                data_source = "last 48h"
        
        if not market_data:
            logger.error("No market data available with any fallback")
            return False
        
        logger.info(f"Using data source: {data_source}")
        
        # Process data
        log_returns = returns_calculator.calculate_log_returns(market_data)
        if log_returns.empty:
            logger.error("Log returns calculation failed")
            return False
        
        cumulative_returns = dispersion_calculator.calculate_cumulative_returns_since_sod(market_data)
        if cumulative_returns.empty:
            logger.error("Cumulative returns calculation failed")
            return False
        
        realized_volatility = dispersion_calculator.calculate_realized_volatility(market_data)
        if realized_volatility.empty:
            logger.error("Realized volatility calculation failed")
            return False
        
        normalized_returns = dispersion_calculator.normalize_returns_by_realized_vol(
            cumulative_returns, realized_volatility
        )
        if normalized_returns.empty:
            logger.error("Normalized returns calculation failed")
            return False
        
        cssd_series = dispersion_calculator.calculate_dispersion_metric(normalized_returns)
        if cssd_series.empty:
            logger.error("CSSD calculation failed")
            return False
        
        # Create chart
        fig = chart_creator.create_portfolio_entry_warning_chart(
            cssd_series=cssd_series,
            normalized_returns_ts=normalized_returns,
            lookback_window=240,
            threshold_percentile=95.0,
            roc_window=15,
            roc_sigma_multiplier=2.0,
            persistence_bars=3,
            pre_quiet_window=60,
            adx_window=14,
            adx_threshold=25.0,
            entry_window_minutes=30
        )
        
        if not fig or not hasattr(fig, 'data'):
            logger.error("Chart creation failed")
            return False
        
        logger.info(f"✅ Warning system test successful!")
        logger.info(f"Data source: {data_source}")
        logger.info(f"CSSD points: {len(cssd_series)}")
        logger.info(f"Chart traces: {len(fig.data)}")
        logger.info(f"Time range: {cssd_series.index[0]} to {cssd_series.index[-1]}")
        
        # Save test chart
        try:
            fig.write_html("weekend_warning_test.html")
            logger.info("Test chart saved as 'weekend_warning_test.html'")
        except Exception as e:
            logger.warning(f"Could not save test chart: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing warning system: {e}")
        return False

def main():
    """Run weekend data tests"""
    logger.info("=== Weekend Data Fallback Test ===")
    
    # Test data availability
    logger.info("\n--- Testing Data Availability ---")
    data_available, data_source = test_weekend_data_fallback()
    
    if not data_available:
        logger.error("❌ No data available with any fallback method")
        return False
    
    logger.info(f"✅ Data available from: {data_source}")
    
    # Test complete warning system
    logger.info("\n--- Testing Complete Warning System ---")
    system_works = test_warning_system_with_fallback()
    
    if not system_works:
        logger.error("❌ Warning system test failed")
        return False
    
    logger.info("\n=== Test Complete ===")
    logger.info("🎉 Weekend fallback working correctly!")
    logger.info("The dashboard should now work even on weekends using Friday's data.")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        logger.error("❌ Weekend test failed")
    else:
        logger.info("✅ Weekend test passed!")
