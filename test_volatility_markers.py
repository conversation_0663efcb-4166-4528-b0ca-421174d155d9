#!/usr/bin/env python3
"""
Test script to verify vertical volatility markers are working.
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from dispersion_charts import DispersionChartCreator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data_with_spikes():
    """Create test data with clear volatility spikes."""
    
    # Create time index (6 hours of minute data)
    start_time = datetime.now() - timedelta(hours=6)
    time_index = pd.date_range(start=start_time, periods=360, freq='1min')
    
    # Currency pairs
    pairs = [
        'EURUSD', 'EURGBP', 'EURAUD', 'EURNZD', 'EURCHF', 'EURCAD', 'EURJPY',
        'GBPUSD', 'GBPAUD', 'GBPNZD', 'GBPCHF', 'GBPCAD', 'GBPJPY',
        'AUDUSD', 'AUDNZD', 'AUDCHF', 'AUDCAD', 'AUDJPY',
        'NZDUSD', 'NZDCHF', 'NZDCAD', 'NZDJPY',
        'USDCHF', 'USDCAD', 'USDJPY',
        'CADCHF', 'CADJPY', 'CHFJPY'
    ]
    
    # Create base random data
    np.random.seed(42)
    base_data = np.random.normal(0, 0.0005, (len(time_index), len(pairs)))
    
    # Add clear volatility spikes at specific times
    spike_times = [60, 120, 180, 240, 300]  # Every hour
    
    for spike_time in spike_times:
        if spike_time < len(time_index):
            # Create a big spike in volatility
            spike_magnitude = np.random.normal(0, 0.01, len(pairs))  # 20x normal volatility
            base_data[spike_time] = spike_magnitude
            
            # Add some follow-through for a few minutes
            for i in range(1, 5):
                if spike_time + i < len(time_index):
                    base_data[spike_time + i] = spike_magnitude * (0.8 ** i)
    
    # Create DataFrame
    df = pd.DataFrame(base_data, index=time_index, columns=pairs)
    
    logger.info(f"Created test data with shape: {df.shape}")
    logger.info(f"Added volatility spikes at times: {spike_times}")
    
    return df

def main():
    logger.info("Starting Volatility Markers Test...")
    
    # Create test data with clear spikes
    test_data = create_test_data_with_spikes()
    
    # Initialize dispersion charts
    charts = DispersionChartCreator()
    
    # Create currency CSSD chart
    logger.info("Creating currency CSSD chart with volatility markers...")
    fig, pair_contributions, currency_weights, sharpe_optimized_allocation = charts.create_currency_cssd_chart(test_data)
    
    # Count traces and shapes
    trace_count = len(fig.data)
    shape_count = len(fig.layout.shapes) if hasattr(fig.layout, 'shapes') and fig.layout.shapes else 0
    
    logger.info(f"Chart created with {trace_count} traces")
    logger.info(f"Chart created with {shape_count} vertical line shapes")
    
    # Count main lines vs other traces
    main_lines = 0
    for trace in fig.data:
        if trace.name and 'Rolling CSSD Dispersion' in trace.name and 'IQR' not in trace.name:
            main_lines += 1
    
    logger.info(f"Main currency dispersion lines: {main_lines}")
    
    # Save chart for inspection
    try:
        fig.write_html("volatility_markers_test.html")
        logger.info("Chart saved as 'volatility_markers_test.html'")
    except Exception as e:
        logger.warning(f"Could not save chart: {e}")
    
    logger.info("✅ Volatility markers test completed!")
    
    if shape_count > 0:
        logger.info(f"✅ Vertical volatility markers are working! Found {shape_count} vertical lines.")
    else:
        logger.warning("⚠️ No vertical lines found. May need to adjust ROC sensitivity.")

if __name__ == "__main__":
    main()
