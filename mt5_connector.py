"""
MT5 Connector Module for Matrix QP
Handles MetaTrader 5 connection and data fetching for portfolio optimization
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import time
import logging
import pytz
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, <PERSON><PERSON>
import psutil

from config import (
    CURRENCY_PAIRS, TIMEFRAME, MT5_TIMEOUT, MAX_RETRIES, RETRY_DELAY,
    MARKET_TIMEZONE, START_OF_DAY_HOUR, DATA_BUFFER_HOURS,
    get_market_start_time
)

# Configure logging
logger = logging.getLogger(__name__)


class MT5Connector:
    """
    MetaTrader 5 connection and data management class
    Provides methods for connecting to MT5 and fetching market data
    """
    
    def __init__(self):
        """Initialize MT5 connector"""
        self.connected = False
        self.last_connection_time = None
        
    def connect(self, retries: int = MAX_RETRIES) -> bool:
        """
        Connect to MetaTrader 5 terminal
        
        Args:
            retries: Number of connection attempts
            
        Returns:
            bool: True if connection successful, False otherwise
        """
        for attempt in range(retries):
            try:
                if mt5.initialize(path=r"E:\\AMP MetaTrader 5\\terminal64.exe", portable=True):
                    self.connected = True
                    self.last_connection_time = datetime.now()
                    logger.info("Successfully connected to MetaTrader 5")
                    
                    # Log account information
                    account_info = mt5.account_info()
                    if account_info:
                        logger.info(f"Connected to account: {account_info.login}")
                        logger.info(f"Server: {account_info.server}")
                        logger.info(f"Currency: {account_info.currency}")
                    
                    return True
                else:
                    error_code = mt5.last_error()
                    logger.warning(f"MT5 connection failed, error code: {error_code}. Attempt {attempt + 1}/{retries}")
                    
            except Exception as e:
                logger.error(f"Exception during MT5 connection attempt {attempt + 1}: {str(e)}")
            
            if attempt < retries - 1:
                logger.info(f"Retrying connection in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)
        
        logger.error("Failed to connect to MetaTrader 5 after all attempts")
        self.connected = False
        return False
    
    def disconnect(self):
        """Disconnect from MetaTrader 5"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            logger.info("Disconnected from MetaTrader 5")
    
    def is_connected(self) -> bool:
        """Check if MT5 connection is active"""
        if not self.connected:
            return False
        
        # Test connection with a simple call
        try:
            mt5.terminal_info()
            return True
        except:
            self.connected = False
            return False
    
    def ensure_connection(self) -> bool:
        """Ensure MT5 connection is active, reconnect if necessary"""
        if not self.is_connected():
            logger.info("MT5 connection lost, attempting to reconnect...")
            return self.connect()
        return True
    
    def fetch_daily_data(self,
                        pairs: Optional[List[str]] = None,
                        start_time: Optional[datetime] = None,
                        current_day_only: bool = True,
                        time_period: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """
        Fetch intraday data from start of trading day

        Args:
            pairs: List of currency pairs (default: all configured pairs)
            start_time: Start time for data collection (default: start of current day)
            current_day_only: If True, filter to only current day data from 00:00
            time_period: Time period string ('2h', '4h', '8h', '24h', '48h', '72h', '120h')

        Returns:
            Dict mapping pair names to DataFrames with OHLC data
        """
        if not self.ensure_connection():
            raise ConnectionError("Cannot establish MT5 connection")
        
        if pairs is None:
            pairs = CURRENCY_PAIRS

        # Handle time period-based data fetching
        if time_period is not None:
            from utils import calculate_time_range_for_period
            start_time, end_time = calculate_time_range_for_period(time_period)
            # Store time period for weekend filtering
            self._current_time_period = time_period
        else:
            if start_time is None:
                start_time = get_market_start_time()
            # Calculate end time (current time)
            end_time = datetime.now(MARKET_TIMEZONE)
            # Clear time period
            self._current_time_period = None
        
        period_info = f" (time period: {time_period})" if time_period else ""
        logger.info(f"Fetching data for {len(pairs)} pairs from {start_time} to {end_time}{period_info}")
        
        # Check CPU usage and throttle if necessary
        current_cpu = psutil.cpu_percent(interval=0.1)
        if current_cpu > 80:
            time.sleep(0.5)
        elif current_cpu > 70:
            time.sleep(0.2)
        
        data = {}
        failed_pairs = []
        
        for pair in pairs:
            try:
                df = self._fetch_pair_data(pair, start_time, end_time)
                if df is not None and not df.empty:
                    data[pair] = df
                    logger.debug(f"Fetched {len(df)} records for {pair}")
                else:
                    failed_pairs.append(pair)
                    logger.warning(f"No data retrieved for {pair}")
                    
            except Exception as e:
                failed_pairs.append(pair)
                logger.error(f"Error fetching data for {pair}: {str(e)}")
        
        if failed_pairs:
            logger.warning(f"Failed to fetch data for pairs: {failed_pairs}")

        # Filter to current day only if requested and no time period specified
        if current_day_only and time_period is None:
            current_day_start = get_market_start_time()
            filtered_data = {}

            for pair, df in data.items():
                # Filter to only data from current day 00:00 onwards
                current_day_data = df[df.index >= current_day_start]
                if not current_day_data.empty:
                    filtered_data[pair] = current_day_data
                    logger.debug(f"Filtered {pair}: {len(df)} -> {len(current_day_data)} records (current day only)")
                else:
                    logger.warning(f"No current day data available for {pair}")

            data = filtered_data
            logger.info(f"Successfully fetched and filtered data for {len(data)} pairs (current day only)")
        else:
            period_desc = f" ({time_period})" if time_period else " (all available)"
            logger.info(f"Successfully fetched data for {len(data)} pairs{period_desc}")

        return data
    
    def _fetch_pair_data(self, 
                        pair: str, 
                        start_time: datetime, 
                        end_time: datetime) -> Optional[pd.DataFrame]:
        """
        Fetch data for a single currency pair
        
        Args:
            pair: Currency pair symbol
            start_time: Start time for data
            end_time: End time for data
            
        Returns:
            DataFrame with OHLC data or None if failed
        """
        try:
            # MT5 has a quirk: it expects UTC timestamps but returns local time data
            # To get the correct time range, we need to add the timezone offset to our request
            offset_hours = start_time.utcoffset().total_seconds() / 3600
            start_adjusted = start_time + timedelta(hours=offset_hours)
            end_adjusted = end_time + timedelta(hours=offset_hours)

            start_timestamp = int(start_adjusted.astimezone(pytz.UTC).timestamp())
            end_timestamp = int(end_adjusted.astimezone(pytz.UTC).timestamp())

            # Fetch rates from MT5
            rates = mt5.copy_rates_range(pair, TIMEFRAME, start_timestamp, end_timestamp)
            
            if rates is None or len(rates) == 0:
                logger.warning(f"No rates returned for {pair}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(rates)
            
            # Convert time column to datetime - treat MT5 time as local time (Europe/Bucharest)
            # MT5 returns local time data, so we localize it to the market timezone
            df['time'] = pd.to_datetime(df['time'], unit='s').dt.tz_localize(MARKET_TIMEZONE)
            
            # Set time as index
            df.set_index('time', inplace=True)
            
            # Sort by time to ensure proper order
            df.sort_index(inplace=True)

            # Filter out weekend data for multi-day periods (passed via instance variable)
            if hasattr(self, '_current_time_period') and self._current_time_period:
                df = self._filter_weekend_data(df, self._current_time_period)
            else:
                df = self._filter_weekend_data(df)

            # Validate data quality
            if self._validate_data_quality(df, pair):
                return df
            else:
                logger.warning(f"Data quality validation failed for {pair}")
                return None
                
        except Exception as e:
            logger.error(f"Error in _fetch_pair_data for {pair}: {str(e)}")
            return None

    def _filter_weekend_data(self, df: pd.DataFrame, time_period: str = None) -> pd.DataFrame:
        """
        Filter out weekend data (Saturday and Sunday) for multi-day periods

        Args:
            df: DataFrame with datetime index
            time_period: Time period string (e.g., '48h', '72h', '120h')

        Returns:
            DataFrame with weekend data removed for multi-day periods
        """
        if df.empty:
            return df

        # Only filter weekends for multi-day periods (> 24h)
        if time_period and time_period.endswith('h'):
            hours = int(time_period[:-1])
            if hours > 24:
                # Filter out weekends (Saturday=5, Sunday=6)
                weekday_mask = df.index.weekday < 5
                filtered_df = df[weekday_mask]

                if len(filtered_df) < len(df):
                    weekend_count = len(df) - len(filtered_df)
                    logger.info(f"Filtered out {weekend_count} weekend data points for {time_period} period")

                return filtered_df

        # For periods <= 24h, return data as-is (weekends already handled in time range calculation)
        return df

    def _validate_data_quality(self, df: pd.DataFrame, pair: str) -> bool:
        """
        Validate the quality of fetched data
        
        Args:
            df: DataFrame to validate
            pair: Currency pair name for logging
            
        Returns:
            bool: True if data quality is acceptable
        """
        if df.empty:
            logger.warning(f"Empty DataFrame for {pair}")
            return False
        
        # Check for required columns
        required_columns = ['open', 'high', 'low', 'close', 'tick_volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.warning(f"Missing columns for {pair}: {missing_columns}")
            return False
        
        # Check for excessive NaN values
        nan_ratio = df['close'].isna().sum() / len(df)
        if nan_ratio > 0.05:  # More than 5% NaN values
            logger.warning(f"Excessive NaN values for {pair}: {nan_ratio:.2%}")
            return False
        
        # Check for zero or negative prices
        if (df['close'] <= 0).any():
            logger.warning(f"Invalid price values detected for {pair}")
            return False
        
        # Check for reasonable price ranges (basic sanity check)
        price_std = df['close'].std()
        price_mean = df['close'].mean()
        if price_std / price_mean > 0.5:  # Coefficient of variation > 50%
            logger.warning(f"Unusual price volatility for {pair}: CV = {price_std/price_mean:.2%}")
        
        return True
    
    def get_symbol_info(self, pair: str) -> Optional[Dict]:
        """
        Get symbol information for a currency pair
        
        Args:
            pair: Currency pair symbol
            
        Returns:
            Dictionary with symbol information or None if failed
        """
        if not self.ensure_connection():
            return None
        
        try:
            symbol_info = mt5.symbol_info(pair)
            if symbol_info is None:
                return None
            
            return {
                'name': symbol_info.name,
                'digits': symbol_info.digits,
                'point': symbol_info.point,
                'spread': symbol_info.spread,
                'trade_tick_value': symbol_info.trade_tick_value,
                'trade_tick_size': symbol_info.trade_tick_size,
                'trade_contract_size': symbol_info.trade_contract_size,
                'currency_base': symbol_info.currency_base,
                'currency_profit': symbol_info.currency_profit,
                'currency_margin': symbol_info.currency_margin
            }
            
        except Exception as e:
            logger.error(f"Error getting symbol info for {pair}: {str(e)}")
            return None
    
    def get_market_status(self) -> Dict[str, bool]:
        """
        Check market status for all configured pairs
        
        Returns:
            Dictionary mapping pair names to trading status
        """
        if not self.ensure_connection():
            return {}
        
        status = {}
        for pair in CURRENCY_PAIRS:
            try:
                symbol_info = mt5.symbol_info(pair)
                if symbol_info:
                    # Check if symbol is available for trading
                    status[pair] = symbol_info.visible and symbol_info.select
                else:
                    status[pair] = False
            except:
                status[pair] = False
        
        return status

    def get_account_info(self) -> Optional[Dict]:
        """
        Get account information
        
        Returns:
            Dictionary with account information or None if failed
        """
        if not self.ensure_connection():
            return None
        
        try:
            account_info = mt5.account_info()
            if account_info is None:
                return None
            
            return {
                'server': account_info.server,
                'balance': account_info.balance,
                'profit': account_info.profit,
                'currency': account_info.currency
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {str(e)}")
            return None
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()

    def close_all_positions(self) -> Dict[str, int]:
        """
        Closes all open positions on MetaTrader 5.

        Returns:
            Dict[str, int]: A dictionary containing the count of 'closed' and 'failed' positions.
        """
        logger.info("=== CLOSE ALL POSITIONS CALLED ===")
        
        if not self.ensure_connection():
            logger.error("Failed to connect to MT5, cannot close positions.")
            return {"closed": 0, "failed": 0}

        logger.info("MT5 connection verified, fetching open positions...")
        
        try:
            positions = mt5.positions_get()
            logger.info(f"MT5 positions_get() returned: {positions}")
            
            if positions is None:
                logger.info("No open positions found (positions_get returned None).")
                return {"closed": 0, "failed": 0}
            
            if len(positions) == 0:
                logger.info("No open positions found (empty positions list).")
                return {"closed": 0, "failed": 0}

            closed_count = 0
            failed_count = 0
            logger.info(f"Found {len(positions)} open positions to close...")

            for i, position in enumerate(positions):
                symbol = position.symbol
                ticket = position.ticket
                position_type = "BUY" if position.type == mt5.ORDER_TYPE_BUY else "SELL"
                volume = position.volume

                logger.info(f"[{i+1}/{len(positions)}] Closing position: Ticket={ticket}, Symbol={symbol}, Type={position_type}, Volume={volume}")

                try:
                    # Get current tick info for the symbol
                    tick_info = mt5.symbol_info_tick(symbol)
                    if tick_info is None:
                        logger.error(f"Failed to get tick info for {symbol}")
                        failed_count += 1
                        continue
                    
                    # Determine the correct price based on position type
                    if position.type == mt5.ORDER_TYPE_BUY:
                        # To close a BUY position, we need to SELL at bid price
                        close_price = tick_info.bid
                        close_type = mt5.ORDER_TYPE_SELL
                    else:
                        # To close a SELL position, we need to BUY at ask price
                        close_price = tick_info.ask
                        close_type = mt5.ORDER_TYPE_BUY

                    # Try different order filling types if the first one fails
                    filling_types = [mt5.ORDER_FILLING_RETURN, mt5.ORDER_FILLING_IOC, mt5.ORDER_FILLING_FOK]
                    
                    success = False
                    for filling_type in filling_types:
                        request = {
                            "action": mt5.TRADE_ACTION_DEAL,
                            "symbol": symbol,
                            "volume": volume,
                            "type": close_type,
                            "position": ticket,
                            "price": close_price,
                            "deviation": 20,  # Slippage in points
                            "magic": 202406,
                            "comment": "Close All",
                            "type_time": mt5.ORDER_TIME_GTC,
                            "type_filling": filling_type,
                        }

                        logger.info(f"Sending close order request (filling type {filling_type}): {request}")
                        result = mt5.order_send(request)
                        
                        # Check for MT5 errors
                        last_error = mt5.last_error()
                        logger.info(f"Order result: {result}, MT5 last_error: {last_error}")

                        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                            logger.info(f"✓ Successfully closed position: Ticket={ticket}, Symbol={symbol}")
                            closed_count += 1
                            success = True
                            break
                        elif result:
                            logger.warning(f"Order failed with retcode {result.retcode}: {result.comment}")
                        else:
                            logger.warning(f"No result returned from order_send with filling type {filling_type}, MT5 error: {last_error}")
                    
                    if not success:
                        # Try market execution as last resort
                        request = {
                            "action": mt5.TRADE_ACTION_DEAL,
                            "symbol": symbol,
                            "volume": volume,
                            "type": close_type,
                            "position": ticket,
                            "deviation": 50,  # Higher slippage for market execution
                            "magic": 202406,
                            "comment": "Close Market",
                            "type_time": mt5.ORDER_TIME_GTC,
                            "type_filling": mt5.ORDER_FILLING_RETURN,
                        }
                        
                        logger.info(f"Trying market execution: {request}")
                        result = mt5.order_send(request)
                        
                        # Check for MT5 errors
                        last_error = mt5.last_error()
                        logger.info(f"Market execution result: {result}, MT5 last_error: {last_error}")
                        
                        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                            logger.info(f"✓ Successfully closed position with market execution: Ticket={ticket}, Symbol={symbol}")
                            closed_count += 1
                        else:
                            error_msg = f"Failed to close position: Ticket={ticket}, Symbol={symbol}"
                            if result:
                                error_msg += f" - Final retcode: {result.retcode}, Comment: {result.comment}"
                            else:
                                error_msg += f" - No result returned from any order attempt, MT5 error: {last_error}"
                            logger.error(error_msg)
                            failed_count += 1
                        
                except Exception as e:
                    logger.error(f"Exception while closing position {ticket}: {str(e)}")
                    failed_count += 1

            logger.info(f"=== CLOSE ALL POSITIONS COMPLETED ===")
            logger.info(f"Final result - Closed: {closed_count}, Failed: {failed_count}")
            return {"closed": closed_count, "failed": failed_count}
            
        except Exception as e:
            logger.error(f"Exception in close_all_positions: {str(e)}")
            return {"closed": 0, "failed": 1}


# Convenience function for quick data fetching
def fetch_current_data(pairs: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
    """
    Convenience function to fetch current day's data
    
    Args:
        pairs: List of currency pairs (default: all configured pairs)
        
    Returns:
        Dictionary mapping pair names to DataFrames
    """
    with MT5Connector() as connector:
        return connector.fetch_daily_data(pairs)


if __name__ == "__main__":
    # Test the connector
    logging.basicConfig(level=logging.INFO)
    
    print("Testing MT5 Connector...")
    
    with MT5Connector() as connector:
        if connector.is_connected():
            print("✓ Connection successful")
            
            # Test market status
            status = connector.get_market_status()
            available_pairs = [pair for pair, available in status.items() if available]
            print(f"✓ Available pairs: {len(available_pairs)}/{len(CURRENCY_PAIRS)}")
            
            # Test data fetching for a few pairs
            test_pairs = CURRENCY_PAIRS[:3]
            data = connector.fetch_daily_data(test_pairs)
            print(f"✓ Data fetched for {len(data)} pairs")
            
            for pair, df in data.items():
                print(f"  {pair}: {len(df)} records")
        else:
            print("✗ Connection failed")
