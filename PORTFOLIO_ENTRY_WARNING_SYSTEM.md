# Portfolio Entry Warning System

## Overview

The Portfolio Entry Warning System is a new chart type designed to help traders identify optimal timing for entering portfolios based on the dispersion breakout strategy outlined in `specs.md`. This system implements a systematic 6-step signal detection process to identify qualified dispersion spikes and manage entry timing.

## Features

### 6-Step Signal Detection Process

1. **CSSD Threshold (T₁)**: Dynamic 95th percentile threshold of last N minutes (default: 240 minutes)
2. **ROC Filter (T₂)**: Rate of change ≥ 2σ within 15 minutes
3. **Persistence**: CSSD stays above T₁ for ≥ 3 bars
4. **Pre-quiet Check**: Previous 60 minutes CSSD < 50th percentile
5. **Trend Confirmation**: ADX > 25 and rising
6. **Entry Window**: 30-minute countdown after qualified signal

### Chart Components

#### Main Subplot: CSSD with Signals
- **White line**: Real-time CSSD dispersion values
- **Red dashed line**: Dynamic T₁ threshold (95th percentile)
- **Yellow dotted line**: 50th percentile reference
- **Green star markers**: Qualified entry signals
- **Green background regions**: 30-minute entry windows

#### ROC Subplot: Rate of Change Analysis
- **Cyan line**: 15-minute rate of change
- **Red dashed lines**: ±2σ bands
- **Gray dotted line**: ROC mean
- **Orange triangles**: ROC spikes above +2σ

#### ADX Subplot: Trend Confirmation
- **Orange line**: ADX calculated from sum of all 28 pairs
- **Yellow dashed line**: ADX 25 threshold
- **Red dotted line**: ADX 50 strong trend reference
- **Green circles**: ADX confirmation points (>25 and rising)

## Usage

### Basic Implementation

```python
from dispersion_charts import DispersionChartCreator

# Initialize chart creator
chart_creator = DispersionChartCreator()

# Create warning system chart
fig = chart_creator.create_portfolio_entry_warning_chart(
    cssd_series=your_cssd_data,
    normalized_returns_ts=your_normalized_returns_df,
    # Optional parameters with defaults:
    lookback_window=240,          # 4 hours
    threshold_percentile=95.0,    # 95th percentile
    roc_window=15,               # 15 minutes
    roc_sigma_multiplier=2.0,    # 2 sigma
    persistence_bars=3,          # 3 bars
    pre_quiet_window=60,         # 1 hour
    adx_window=14,              # 14 periods
    adx_threshold=25.0,         # ADX > 25
    entry_window_minutes=30     # 30 minutes
)

# Display or save the chart
fig.show()
# or
fig.write_html("warning_system.html")
```

### Parameter Customization

#### Signal Detection Parameters
- `lookback_window`: Window for threshold calculations (default: 240 minutes)
- `threshold_percentile`: Percentile for T₁ threshold (default: 95.0)
- `roc_window`: Window for ROC calculation (default: 15 minutes)
- `roc_sigma_multiplier`: Sigma multiplier for ROC filter (default: 2.0)
- `persistence_bars`: Required bars above threshold (default: 3)
- `pre_quiet_window`: Window for pre-quiet check (default: 60 minutes)

#### Trend Confirmation Parameters
- `adx_window`: Window for ADX calculation (default: 14)
- `adx_threshold`: ADX threshold for trend confirmation (default: 25.0)

#### Entry Management Parameters
- `entry_window_minutes`: Entry window duration (default: 30 minutes)

## Signal Interpretation

### Qualified Entry Signals (Green Stars)
A qualified entry signal appears when ALL conditions are met:
1. ✅ CSSD ≥ T₁ threshold
2. ✅ ROC spike ≥ +2σ band
3. ✅ CSSD persistence for 3+ bars
4. ✅ Pre-quiet period confirmed
5. ✅ ADX > 25 and rising
6. ✅ No recent signal within entry window

### Entry Windows (Green Background)
- **Duration**: 30 minutes after qualified signal
- **Action**: Enter pre-computed MPT portfolios
- **Portfolio Selection**: Based on breakout characteristics (Max Sharpe, Sortino, Omega, Min Var)

### Color-Coded Indicators
- **Green**: Entry signals and windows
- **Red**: Threshold breaches and sigma bands
- **Yellow**: Reference levels (50th percentile, ADX 25)
- **Orange**: ROC spikes and ADX line
- **Cyan**: ROC main line
- **White**: Main CSSD line

## Integration with Trading Strategy

### Real-Time Monitoring
1. Monitor the chart for green star signals
2. Verify all 6 conditions are met
3. Enter portfolio within 30-minute window
4. Use appropriate portfolio type based on market conditions

### Portfolio Selection Logic
- **Clear breakout + strong trend**: Max Sharpe/Sortino
- **Wide volatility + mixed signals**: Omega portfolio
- **Unclear direction**: Min Variance (often skip trade)

### Risk Management
- **Position sizing**: Based on ATR and equity
- **Initial stops**: 1.2 × ATR from entry
- **Max portfolio VaR**: ≤ 2% account equity

## Testing

Run the test script to see the warning system in action:

```bash
python test_warning_system.py
```

This generates sample data with artificial breakout patterns and creates a demonstration chart saved as `portfolio_entry_warning_system.html`.

## Technical Notes

### ADX Calculation
- Uses sum of all 28 currency pairs' normalized returns
- Creates synthetic OHLC data for ADX calculation
- Provides market-wide trend strength indication

### Signal Filtering
- Prevents duplicate signals within entry windows
- Requires all 6 conditions simultaneously
- Implements proper lookback periods for statistical validity

### Performance Considerations
- Optimized for real-time data processing
- Efficient rolling calculations
- Minimal memory footprint for long time series
