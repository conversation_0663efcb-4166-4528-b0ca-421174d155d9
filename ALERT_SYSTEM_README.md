# Matrix QP Alert System

## Overview

The Matrix QP Alert System detects when multiple currency pairs start "taking off together" (like birds getting ready to fly) during periods of synchronized movement. This helps identify potential market breakout moments when >5 pairs begin accelerating simultaneously.

## Features

### 🚀 Takeoff Detection
- **Real-time monitoring**: Checks every 60 seconds via the dispersion interval
- **Multi-pair analysis**: Detects when ≥5 pairs start accelerating together
- **Smart thresholds**: Configurable acceleration, magnitude, and velocity criteria
- **Cooldown protection**: Prevents spam alerts with 5-minute cooldown periods

### 📢 Alert Delivery
- **Desktop notifications**: Native system notifications via plyer
- **Sound alerts**: System beep alerts
- **Dashboard indicators**: Visual alerts in the web interface
- **Alert history**: Maintains last 100 alerts for review

### ⚙️ Configuration
- **Adjustable thresholds**: Min pairs (default: 5), acceleration (default: 2.0), magnitude (default: 0.5)
- **Dashboard controls**: Easy-to-use settings panel in the web interface
- **Persistent settings**: Configuration saved to `alert_config.json`

## How It Works

### Detection Algorithm
1. **Data Input**: Uses normalized log returns from the dispersion calculator
2. **Acceleration Calculation**: Computes second derivative (rate of change of rate of change)
3. **Multi-criteria Filtering**:
   - **Acceleration**: Must exceed threshold (default: 2.0)
   - **Magnitude**: Absolute value must exceed threshold (default: 0.5)
   - **Velocity**: First derivative must exceed 50% of magnitude threshold
4. **Threshold Check**: Triggers alert when ≥5 pairs meet all criteria simultaneously

### Integration Points
- **Dashboard Callback**: Integrated into `update_dispersion_charts()` function
- **Real-time Updates**: Runs every 60 seconds with the dispersion interval
- **UI Components**: Alert indicator and settings panel in dashboard layout

## Installation & Setup

### Dependencies
```bash
pip install plyer  # For desktop notifications
```

### Files Added
- `alert_system.py` - Core alert detection and management logic
- `test_alerts.py` - Test suite for validation
- `alert_config.json` - Configuration storage (auto-created)

### Dashboard Integration
- Alert indicator added to main dashboard layout
- Settings panel with configurable thresholds
- Real-time alert display with pair details

## Usage

### Dashboard Interface
1. **View Alerts**: Alert indicator appears at top of dashboard when triggered
2. **Configure Settings**: Expand "Alert Settings" section to adjust thresholds
3. **Update Configuration**: Click "Update Settings" to apply changes

### Alert Information
When triggered, alerts show:
- 🚀 Visual indicator with rocket emoji
- Number of pairs involved
- List of accelerating pairs (first 5 shown)
- Timestamp and magnitude information

### Settings
- **Min Pairs for Alert**: Minimum number of pairs needed (3-15, default: 5)
- **Acceleration Threshold**: Minimum acceleration factor (0.5-5.0, default: 2.0)
- **Magnitude Threshold**: Minimum normalized return magnitude (auto-calculated)

## Testing

Run the test suite to verify functionality:
```bash
cd matrix_QP
python test_alerts.py
```

The test creates simulated takeoff patterns and validates:
- ✅ Detection algorithm accuracy
- ✅ Alert delivery mechanisms
- ✅ Configuration management
- ✅ Desktop notifications

## Technical Details

### Key Classes
- **`TakeoffDetector`**: Core detection algorithm
- **`AlertManager`**: Handles delivery and configuration
- **`AlertEvent`**: Data structure for alert information

### Detection Criteria
```python
# Conditions for takeoff detection:
1. Positive acceleration > threshold
2. Sufficient magnitude > threshold  
3. Sufficient velocity > 50% of magnitude threshold
4. Minimum number of pairs meeting criteria
```

### Performance
- **Minimal overhead**: Runs only during existing dispersion calculations
- **Efficient computation**: Uses pandas vectorized operations
- **Memory conscious**: Limited alert history (100 items max)

## Customization

### Threshold Tuning
- **Lower thresholds**: More sensitive, more alerts
- **Higher thresholds**: Less sensitive, fewer false positives
- **Recommended starting point**: Default values work well for most scenarios

### Alert Channels
Current: Desktop notifications, sound, dashboard
Future: Email, SMS, webhook integrations (easily extensible)

### Detection Logic
The algorithm can be extended to detect other patterns:
- Divergence detection (pairs moving in opposite directions)
- Volatility spikes
- Correlation breakdowns

## Troubleshooting

### No Alerts Detected
- Check if thresholds are too high
- Verify sufficient market volatility
- Ensure ≥5 pairs have data

### Too Many Alerts
- Increase acceleration threshold
- Increase minimum pairs requirement
- Check cooldown period settings

### Desktop Notifications Not Working
- Ensure `plyer` is installed
- Check system notification permissions
- Verify Windows notification settings

## Future Enhancements

### Planned Features
- Email alert integration
- Custom alert sounds
- Historical alert analysis
- Pattern recognition improvements
- Mobile app notifications

### API Extensions
- REST endpoints for external integrations
- Webhook support for trading platforms
- Real-time streaming alerts

---

**Note**: This alert system is designed to complement your existing dispersion analysis workflow and help identify potential market breakout moments when multiple currency pairs begin moving together in a synchronized fashion.
