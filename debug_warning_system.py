"""
Debug script to test the Portfolio Entry Warning System data pipeline
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mt5_connection():
    """Test MT5 connection and data availability"""
    try:
        from mt5_connector import MT5Connector
        
        logger.info("Testing MT5 connection...")
        mt5_connector = MT5Connector()
        
        # Test connection
        if hasattr(mt5_connector, 'connect'):
            connected = mt5_connector.connect()
            logger.info(f"MT5 connection status: {connected}")
        
        # Test data fetch
        logger.info("Fetching market data...")
        market_data = mt5_connector.fetch_daily_data(current_day_only=True)
        
        if market_data:
            logger.info(f"Market data available: {len(market_data)} records")
            logger.info(f"Data keys: {list(market_data.keys())}")
            
            # Check first few records
            for symbol, data in list(market_data.items())[:3]:
                if isinstance(data, pd.DataFrame) and not data.empty:
                    logger.info(f"{symbol}: {len(data)} rows, latest: {data.index[-1]}")
                else:
                    logger.warning(f"{symbol}: No data or empty DataFrame")
            
            return True, market_data
        else:
            logger.error("No market data returned")
            return False, None
            
    except Exception as e:
        logger.error(f"Error testing MT5 connection: {e}")
        return False, None

def test_returns_calculation(market_data):
    """Test log returns calculation"""
    try:
        from log_returns import LogReturnsCalculator
        
        logger.info("Testing log returns calculation...")
        returns_calculator = LogReturnsCalculator()
        
        log_returns = returns_calculator.calculate_log_returns(market_data)
        
        if log_returns.empty:
            logger.error("Log returns calculation returned empty DataFrame")
            return False, None
        else:
            logger.info(f"Log returns calculated: {log_returns.shape}")
            logger.info(f"Columns: {list(log_returns.columns)}")
            logger.info(f"Time range: {log_returns.index[0]} to {log_returns.index[-1]}")
            return True, log_returns
            
    except Exception as e:
        logger.error(f"Error calculating log returns: {e}")
        return False, None

def test_dispersion_calculation(market_data):
    """Test dispersion calculation pipeline"""
    try:
        from dispersion_calculator import DispersionCalculator
        
        logger.info("Testing dispersion calculation...")
        dispersion_calc = DispersionCalculator()
        
        # Calculate cumulative returns
        cumulative_returns = dispersion_calc.calculate_cumulative_returns_since_sod(market_data)
        if cumulative_returns.empty:
            logger.error("Cumulative returns calculation failed")
            return False, None
        
        logger.info(f"Cumulative returns: {cumulative_returns.shape}")
        
        # Calculate realized volatility
        realized_volatility = dispersion_calc.calculate_realized_volatility(market_data)
        if realized_volatility.empty:
            logger.error("Realized volatility calculation failed")
            return False, None
            
        logger.info(f"Realized volatility: {realized_volatility.shape}")
        
        # Normalize returns
        normalized_returns = dispersion_calc.normalize_returns_by_realized_vol(
            cumulative_returns, realized_volatility
        )
        if normalized_returns.empty:
            logger.error("Normalized returns calculation failed")
            return False, None
            
        logger.info(f"Normalized returns: {normalized_returns.shape}")
        
        # Calculate CSSD
        cssd_series = dispersion_calc.calculate_dispersion_metric(normalized_returns)
        if cssd_series.empty:
            logger.error("CSSD calculation failed")
            return False, None
            
        logger.info(f"CSSD series: {len(cssd_series)} points")
        logger.info(f"CSSD range: {cssd_series.min():.6f} to {cssd_series.max():.6f}")
        
        return True, (cssd_series, normalized_returns)
        
    except Exception as e:
        logger.error(f"Error in dispersion calculation: {e}")
        return False, None

def test_chart_creation(cssd_series, normalized_returns):
    """Test warning system chart creation"""
    try:
        from dispersion_charts import DispersionChartCreator
        
        logger.info("Testing chart creation...")
        chart_creator = DispersionChartCreator()
        
        # Create chart with default parameters
        fig = chart_creator.create_portfolio_entry_warning_chart(
            cssd_series=cssd_series,
            normalized_returns_ts=normalized_returns,
            lookback_window=240,
            threshold_percentile=95.0,
            roc_window=15,
            roc_sigma_multiplier=2.0,
            persistence_bars=3,
            pre_quiet_window=60,
            adx_window=14,
            adx_threshold=25.0,
            entry_window_minutes=30
        )
        
        if fig and hasattr(fig, 'data'):
            logger.info(f"Chart created successfully with {len(fig.data)} traces")
            return True, fig
        else:
            logger.error("Chart creation failed - no figure returned")
            return False, None
            
    except Exception as e:
        logger.error(f"Error creating chart: {e}")
        return False, None

def create_sample_data():
    """Create sample data for testing when real data is not available"""
    logger.info("Creating sample data for testing...")
    
    # Create time series for last 4 hours
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=4)
    time_index = pd.date_range(start=start_time, end=end_time, freq='1min')
    
    # Currency pairs
    currency_pairs = [
        'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDCHF', 'USDJPY',
        'EURGBP', 'EURAUD', 'EURNZD', 'EURCAD', 'EURCHF', 'EURJPY',
        'GBPAUD', 'GBPNZD', 'GBPCAD', 'GBPCHF', 'GBPJPY',
        'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY',
        'NZDCAD', 'NZDCHF', 'NZDJPY',
        'CADCHF', 'CADJPY', 'CHFJPY'
    ]
    
    # Generate sample normalized returns
    np.random.seed(42)
    normalized_returns = {}
    for pair in currency_pairs:
        # Add some correlation and occasional spikes
        base_returns = np.random.normal(0, 0.001, len(time_index))
        # Add some breakout patterns
        if np.random.random() > 0.8:  # 20% chance of breakout pattern
            spike_start = np.random.randint(60, len(time_index) - 60)
            base_returns[spike_start:spike_start+30] += np.random.normal(0.005, 0.002, 30)
        
        normalized_returns[pair] = base_returns
    
    normalized_returns_df = pd.DataFrame(normalized_returns, index=time_index)
    
    # Calculate sample CSSD
    cssd_values = normalized_returns_df.std(axis=1)
    cssd_series = pd.Series(cssd_values, index=time_index, name='CSSD')
    
    logger.info(f"Sample data created: {normalized_returns_df.shape}")
    logger.info(f"CSSD range: {cssd_series.min():.6f} to {cssd_series.max():.6f}")
    
    return cssd_series, normalized_returns_df

def main():
    """Run diagnostic tests"""
    logger.info("=== Portfolio Entry Warning System Diagnostic ===")
    
    # Test 1: MT5 Connection
    logger.info("\n1. Testing MT5 Connection...")
    mt5_success, market_data = test_mt5_connection()
    
    if not mt5_success:
        logger.warning("MT5 connection failed, using sample data for testing...")
        cssd_series, normalized_returns = create_sample_data()
    else:
        # Test 2: Returns Calculation
        logger.info("\n2. Testing Returns Calculation...")
        returns_success, log_returns = test_returns_calculation(market_data)
        
        if not returns_success:
            logger.error("Returns calculation failed")
            return False
        
        # Test 3: Dispersion Calculation
        logger.info("\n3. Testing Dispersion Calculation...")
        dispersion_success, dispersion_data = test_dispersion_calculation(market_data)
        
        if not dispersion_success:
            logger.error("Dispersion calculation failed")
            return False
        
        cssd_series, normalized_returns = dispersion_data
    
    # Test 4: Chart Creation
    logger.info("\n4. Testing Chart Creation...")
    chart_success, fig = test_chart_creation(cssd_series, normalized_returns)
    
    if not chart_success:
        logger.error("Chart creation failed")
        return False
    
    logger.info("\n=== Diagnostic Complete ===")
    logger.info("✅ All tests passed! The warning system should work correctly.")
    
    # Save test chart
    try:
        fig.write_html("debug_warning_chart.html")
        logger.info("Test chart saved as 'debug_warning_chart.html'")
    except Exception as e:
        logger.warning(f"Could not save test chart: {e}")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        logger.error("❌ Diagnostic failed - check the errors above")
    else:
        logger.info("🎉 Diagnostic successful - warning system should work!")
