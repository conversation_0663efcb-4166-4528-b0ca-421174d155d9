"""
Dispersion Calculation Module for Matrix QP
Handles calculation of dispersion metrics for portfolio analysis
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta

from config import CURRENCY_PAIRS, CURRENCY_COLORS

# Configure logging
logger = logging.getLogger(__name__)


class DispersionCalculator:
    """
    Calculator for dispersion metrics used in portfolio analysis
    """
    
    def __init__(self):
        """Initialize the dispersion calculator"""
        self.last_calculation_time = None
        self.cached_dispersion = None
        self.cached_rolling_dispersion = None
    
    def calculate_dispersion_metric(self, normalized_returns_ts: pd.DataFrame) -> pd.Series:
        """
        Calculate the dispersion metric (standard deviation across normalized returns at each time point)
        
        Args:
            normalized_returns_ts: DataFrame with normalized log returns (columns: pairs, index: timestamps)
            
        Returns:
            Series with dispersion values over time
        """
        if not isinstance(normalized_returns_ts, pd.DataFrame) or normalized_returns_ts.empty:
            logger.warning("Invalid or empty normalized returns for dispersion calculation")
            return pd.Series(dtype=float)

        # Calculate standard deviation across columns (axis=1) for each row (time point)
        dispersion_ts = normalized_returns_ts.std(axis=1, ddof=1)  # Use sample standard deviation
        
        logger.info(f"Calculated dispersion metric with {len(dispersion_ts)} data points")
        return dispersion_ts
    
    def calculate_rolling_dispersion(self, normalized_returns_ts: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate rolling dispersion for each currency pair using expanding standard deviation per day

        Args:
            normalized_returns_ts: DataFrame with normalized cumulative returns (columns: pairs, index: timestamps)

        Returns:
            DataFrame with rolling dispersion for each pair
        """
        if not isinstance(normalized_returns_ts, pd.DataFrame) or normalized_returns_ts.empty:
            logger.warning("Invalid or empty normalized returns for rolling dispersion calculation")
            return pd.DataFrame()

        # Compute rolling dispersion for each pair (expanding std per day) using normalized returns
        rolling_dispersion_df = pd.DataFrame(index=normalized_returns_ts.index)

        for pair in normalized_returns_ts.columns:
            series = normalized_returns_ts[pair]
            # Group by date, expanding std per day
            rolling = series.groupby(series.index.date).expanding().std(ddof=1).reset_index(level=0, drop=True)
            rolling_dispersion_df[pair] = rolling

        # Forward fill the calculated rolling dispersion to extend lines
        rolling_dispersion_df = rolling_dispersion_df.ffill()

        logger.info(f"Calculated rolling dispersion for {len(rolling_dispersion_df.columns)} pairs")
        return rolling_dispersion_df

    def calculate_rolling_cssd_dispersion(self, cssd_series: pd.Series) -> pd.Series:
        """
        Calculate rolling dispersion (expanding std) of CSSD values since the start of each day
        This measures how volatile the dispersion itself is over time

        Args:
            cssd_series: Series with CSSD values indexed by timestamp

        Returns:
            Series with rolling dispersion of CSSD values
        """
        if not isinstance(cssd_series, pd.Series) or cssd_series.empty:
            logger.warning("Invalid or empty CSSD series for rolling CSSD dispersion calculation")
            return pd.Series(dtype=float)

        # Group by date and calculate expanding standard deviation of CSSD values
        rolling_cssd_dispersion = cssd_series.groupby(cssd_series.index.date).expanding().std(ddof=1).reset_index(level=0, drop=True)

        logger.info(f"Calculated rolling dispersion of CSSD with {len(rolling_cssd_dispersion)} data points")
        return rolling_cssd_dispersion
    
    def calculate_cumulative_returns_since_sod(self, market_data: Dict) -> pd.DataFrame:
        """
        Calculate cumulative log returns from start of day for each pair

        Args:
            market_data: Dictionary mapping pair names to DataFrames with OHLC data

        Returns:
            DataFrame with cumulative log returns since start of day
        """
        if not market_data:
            return pd.DataFrame()

        cumulative_returns = {}
        common_index = None

        for pair, df in market_data.items():
            if not isinstance(df, pd.DataFrame) or df.empty or 'close' not in df.columns:
                logger.warning(f"Skipping {pair}: invalid or empty DataFrame")
                continue

            df_sorted = df.sort_index()

            if len(df_sorted) < 1:
                logger.warning(f"Not enough data points for {pair}")
                continue

            # Get start of day price
            sod_price = df_sorted['close'].iloc[0]

            # Check for invalid SOD price
            if pd.isna(sod_price) or sod_price <= 0:
                logger.warning(f"Invalid SOD price for {pair}: {sod_price}")
                continue

            close_prices = pd.to_numeric(df_sorted['close'], errors='coerce')

            # Replace non-positive prices with NaN
            close_prices_positive = close_prices.where(close_prices > 0, np.nan)

            # Calculate cumulative log returns from SOD
            cumulative_log_returns = np.log(close_prices_positive / sod_price)

            # Check if all returns became NaN
            if cumulative_log_returns.isnull().all():
                logger.warning(f"All prices for {pair} were non-positive after SOD")
                continue

            cumulative_returns[pair] = cumulative_log_returns

            if common_index is None:
                common_index = cumulative_log_returns.index
            else:
                common_index = common_index.union(cumulative_log_returns.index)

        if not cumulative_returns or common_index is None:
            logger.error("Could not calculate any valid cumulative returns")
            return pd.DataFrame()

        # Combine into single DataFrame
        combined_df = pd.DataFrame(index=common_index)
        for pair, ts in cumulative_returns.items():
            combined_df[pair] = ts.reindex(common_index).ffill()

        # Backfill any remaining NaNs at the beginning
        combined_df = combined_df.bfill()

        logger.info(f"Calculated cumulative returns since SOD for {len(combined_df.columns)} pairs")
        return combined_df

    def calculate_realized_volatility(self, market_data: Dict) -> pd.Series:
        """
        Calculate realized volatility for each pair based on minute-by-minute log returns

        Args:
            market_data: Dictionary mapping pair names to DataFrames with OHLC data

        Returns:
            Series with realized volatility for each pair
        """
        if not market_data:
            return pd.Series(dtype=float)

        # Calculate minute-by-minute log returns for each pair
        pair_log_returns = {}
        common_index = None

        for pair, df in market_data.items():
            if not isinstance(df, pd.DataFrame) or df.empty or 'close' not in df.columns:
                continue

            if len(df) < 2:
                continue

            # Ensure prices are positive before log calculation
            safe_close = df['close'].where(df['close'] > 0)

            # Calculate minute-by-minute log returns
            log_returns = np.log(safe_close / safe_close.shift(1)).dropna()

            if not log_returns.empty:
                pair_log_returns[pair] = log_returns
                if common_index is None:
                    common_index = log_returns.index
                else:
                    common_index = common_index.union(log_returns.index)

        if not pair_log_returns or common_index is None:
            logger.warning("No valid minute-by-minute log returns found")
            return pd.Series(dtype=float)

        # Combine into DataFrame
        log_returns_df = pd.DataFrame(index=common_index)
        for pair, log_rets in pair_log_returns.items():
            log_returns_df[pair] = log_rets.reindex(common_index)

        # Fill NaNs with 0 (no change)
        log_returns_df = log_returns_df.ffill(limit=5).bfill(limit=5).fillna(0.0)

        # Calculate realized volatility for each pair
        realized_vol = pd.Series(dtype=float)

        for pair in log_returns_df.columns:
            log_returns = log_returns_df[pair].dropna()

            if log_returns.empty:
                realized_vol[pair] = np.nan
                continue

            # Realized volatility = sqrt(sum(log_returns^2))
            sum_sq_returns = (log_returns ** 2).sum()

            if sum_sq_returns > 0:
                realized_vol[pair] = np.sqrt(sum_sq_returns)
            elif sum_sq_returns == 0:
                realized_vol[pair] = 0.0
            else:
                logger.warning(f"Negative sum of squared returns for {pair}")
                realized_vol[pair] = np.nan

        logger.info(f"Calculated realized volatility for {len(realized_vol)} pairs")
        return realized_vol

    def normalize_returns_by_realized_vol(self,
                                        cumulative_returns: pd.DataFrame,
                                        realized_volatility: pd.Series) -> pd.DataFrame:
        """
        Normalize cumulative returns by realized volatility

        Args:
            cumulative_returns: DataFrame with cumulative log returns since SOD
            realized_volatility: Series with realized volatility for each pair

        Returns:
            DataFrame with normalized returns
        """
        if cumulative_returns.empty or realized_volatility.empty:
            return pd.DataFrame()

        # Replace 0 volatility with NaN to avoid division by zero
        volatility_no_zero = realized_volatility.replace(0, np.nan)

        # Ensure we only divide by valid volatilities
        valid_volatility = volatility_no_zero.dropna()

        if valid_volatility.empty:
            logger.warning("No valid realized volatilities available for normalization")
            return pd.DataFrame(index=cumulative_returns.index)

        # Align columns - only normalize pairs present in both
        common_pairs = cumulative_returns.columns.intersection(valid_volatility.index)

        if not common_pairs.tolist():
            logger.warning("No common pairs found between returns and volatilities")
            return pd.DataFrame(index=cumulative_returns.index)

        # Select and align data for common pairs
        returns_to_normalize = cumulative_returns[common_pairs]
        volatility_aligned = valid_volatility[common_pairs]

        # Perform normalization using broadcasting
        normalized_returns = returns_to_normalize.div(volatility_aligned, axis=1)

        # Add back columns that couldn't be normalized (as NaN)
        missing_pairs = cumulative_returns.columns.difference(common_pairs)
        if not missing_pairs.empty:
            normalized_returns[missing_pairs] = np.nan

        # Ensure original column order is preserved
        normalized_returns = normalized_returns.reindex(columns=cumulative_returns.columns)

        logger.info(f"Normalized returns by realized volatility for {len(common_pairs)} pairs")
        return normalized_returns
    
    def calculate_retracement_percentage(self, dispersion_ts: pd.Series) -> pd.Series:
        """
        Calculate retracement percentage from historical maximum
        
        Args:
            dispersion_ts: Series with dispersion values
            
        Returns:
            Series with retracement percentages
        """
        if dispersion_ts.empty:
            return pd.Series(dtype=float)
        
        # Calculate running maximum
        running_max = dispersion_ts.cummax()
        
        # Calculate current retracement from historical maximum ((max_so_far - current) / max_so_far) * 100
        retracement_pct = ((running_max - dispersion_ts) / running_max.replace(0, np.nan) * 100).fillna(0)
        retracement_pct[retracement_pct < 0] = 0  # Safety check for dispersion
        
        logger.info(f"Calculated retracement percentages with max retracement: {retracement_pct.max():.2f}%")
        return retracement_pct
    
    def get_pair_color(self, pair: str, last_return_value: float) -> str:
        """
        Get color for a currency pair based on the last return value
        
        Args:
            pair: Currency pair (e.g., 'EURUSD')
            last_return_value: Last normalized return value for the pair
            
        Returns:
            Color string based on which currency is performing better
        """
        if len(pair) != 6:
            return CURRENCY_COLORS.get('USD', '#2ca02c')  # Default to USD green

        base_currency = pair[:3]
        quote_currency = pair[3:6]

        # Positive returns mean base currency is strengthening vs quote
        # Negative returns mean quote currency is strengthening vs base
        if last_return_value >= 0:
            # Base currency is stronger, use base currency color
            return CURRENCY_COLORS.get(base_currency, CURRENCY_COLORS.get('USD', '#2ca02c'))
        else:
            # Quote currency is stronger, use quote currency color
            return CURRENCY_COLORS.get(quote_currency, CURRENCY_COLORS.get('USD', '#2ca02c'))
    
    def calculate_near_peak_pairs(self, 
                                rolling_dispersion_df: pd.DataFrame, 
                                normalized_returns_ts: pd.DataFrame,
                                threshold: float = 0.95) -> Tuple[Dict[str, float], Dict[str, float]]:
        """
        Calculate pairs that are near their peak rolling dispersion and those at exact peak
        
        Args:
            rolling_dispersion_df: DataFrame with rolling dispersion values
            normalized_returns_ts: DataFrame with normalized returns
            threshold: Threshold for "near peak" (default 0.95 = within 5% of peak)
            
        Returns:
            Tuple of (near_peak_pairs, star_pairs) dictionaries with pair weights
        """
        near_peak_pairs = {}
        star_pairs = {}
        
        if rolling_dispersion_df.empty or normalized_returns_ts.empty:
            return near_peak_pairs, star_pairs
        
        # Sort pairs by their final rolling dispersion value for processing order
        if not rolling_dispersion_df.empty:
            final_values = rolling_dispersion_df.iloc[-1].dropna()
            sorted_pairs_by_final_value = final_values.sort_values(ascending=False).index.tolist()
        else:
            sorted_pairs_by_final_value = rolling_dispersion_df.columns.tolist()
        
        for pair in sorted_pairs_by_final_value:
            y = rolling_dispersion_df[pair]
            peak_val = y.max() if not y.isnull().all() else np.nan
            latest_val = y.iloc[-1] if not y.empty else np.nan
            
            # Check if current value is within threshold of peak
            is_near_peak = (peak_val != 0 and not pd.isna(latest_val) and not pd.isna(peak_val)
                           and latest_val >= peak_val * threshold)
            
            # Check if current value equals peak value
            is_at_peak = (not y.empty and np.isclose(latest_val, peak_val, atol=1e-8, equal_nan=True))
            
            if is_near_peak:
                # Get latest normalized return
                last_norm_return = normalized_returns_ts[pair].dropna()
                if not last_norm_return.empty:
                    last_norm_return = last_norm_return.iloc[-1]
                    near_peak_pairs[pair] = last_norm_return
                    
            if is_at_peak:  # Add to star_pairs if at exact peak
                last_norm_return = normalized_returns_ts[pair].dropna()
                if not last_norm_return.empty:
                    last_norm_return = last_norm_return.iloc[-1]
                    star_pairs[pair] = last_norm_return
        
        logger.info(f"Found {len(near_peak_pairs)} near-peak pairs and {len(star_pairs)} star pairs")
        return near_peak_pairs, star_pairs
    
    def format_weights_string(self, pairs_dict: Dict[str, float]) -> str:
        """
        Format pairs and weights into a comma-separated string
        
        Args:
            pairs_dict: Dictionary of pair names to weight values
            
        Returns:
            Formatted string like "EURUSD:0.1234,GBPUSD:-0.0567"
        """
        if not pairs_dict:
            return ""
        
        # Calculate sum of absolute values for normalization
        abs_sum = sum(abs(val) for val in pairs_dict.values())
        if abs_sum == 0:  # Avoid division by zero
            return ""
        
        # Normalize weights
        normalized_weights = {pair: val / abs_sum for pair, val in pairs_dict.items()}
        
        # Sort by absolute weight values while preserving signs
        sorted_weights = dict(sorted(normalized_weights.items(), key=lambda item: abs(item[1]), reverse=True))
        
        # Format as string
        weights_str = ",".join([f"{pair}:{weight:.4f}" for pair, weight in sorted_weights.items()])
        
        return weights_str
