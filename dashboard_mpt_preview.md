# Dashboard MPT Allocation Format Preview

## What You'll See in the Dashboard

Below the "Pairs Contributions to CSSD" table, you'll now see a new section:

---

### MPT Allocation Format (Copy-Ready):

**USD:** `[USDJPY:0.358,AUDUSD:0.358,EURUSD:0.284                    ]` ← Readonly input box with green border

**EUR:** `[EURCHF:0.340,EURNZD:0.338,EURUSD:0.322                    ]` ← Readonly input box with blue border

**GBP:** `[GBPAUD:0.342,GBPJPY:0.338,GBPNZD:0.321                    ]` ← Readonly input box with red border

**AUD:** `[AUDNZD:0.384,GBPAUD:0.311,AUDUSD:0.305                    ]` ← Readonly input box with orange border

**NZD:** `[AUDNZD:0.396,EURNZD:0.345,NZDJPY:0.260                    ]` ← Readonly input box with aqua border

**CAD:** `[CADCHF:0.355,EURCAD:0.339,CADJPY:0.306                    ]` ← Readonly input box with pink border

**CHF:** `[AUDCHF:0.390,EURCHF:0.365,USDCHF:0.245                    ]` ← Readonly input box with gray border

**JPY:** `[USDJPY:0.360,NZDJPY:0.324,GBPJPY:0.315                    ]` ← Readonly input box with yellow border

*Click in any box to select all text for easy copying. Format: PAIR1:weight,PAIR2:weight,PAIR3:weight*

---

## Features:

✅ **Copy-Ready Format**: Exact MPT format used in optimization boxes
✅ **Readonly Input Boxes**: Click to select all text for easy copying
✅ **Currency-Colored Borders**: Each currency has its distinctive color border
✅ **Monospace Font**: Clear, readable format like code editors
✅ **Top 3 Pairs**: Shows the 3 most contributing pairs per currency
✅ **Normalized Weights**: All weights sum to 1.0 for each currency
✅ **Real-Time Updates**: Updates every minute with live market data

## Usage:

1. **View Current Market Conditions**: See which pairs are contributing most to each currency's dispersion
2. **Copy for Optimization**: Click any input box and Ctrl+C to copy the MPT string
3. **Paste into Optimization**: Use directly in portfolio optimization tools
4. **Monitor Changes**: Watch how the top contributing pairs change throughout the trading day

## Example Copy-Paste Workflow:

1. Click on the USD input box: `USDJPY:0.358,AUDUSD:0.358,EURUSD:0.284`
2. Press Ctrl+A (select all) then Ctrl+C (copy)
3. Paste directly into your optimization tool
4. The weights are already normalized to 1.0 and ready to use!
