"""
Portfolio Presentation Module for Matrix QP
Handles the presentation and formatting of optimized portfolios
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime

from config import (
    MIN_PORTFOLIOS, PORTFOLIO_SIZE, MAX_CURRENCY_OCCURRENCE,
    OPTIMIZATION_STRATEGIES, validate_portfolio_distribution
)
from portfolio_optimizer import Portfolio
from weight_manager import WeightManager
from risk_calculator import RiskCalculator

# Configure logging
logger = logging.getLogger(__name__)


class PortfolioPresenter:
    """
    Portfolio presentation and formatting system
    Handles the display and analysis of optimized portfolios
    """
    
    def __init__(self):
        """Initialize the portfolio presenter"""
        self.weight_manager = WeightManager()
        self.risk_calculator = RiskCalculator()
        self.presentation_history = []
    
    def present_portfolios(self, 
                         portfolios: List[Portfolio],
                         log_returns: pd.DataFrame,
                         total_lot_size: float = 1.0) -> Dict:
        """
        Present optimized portfolios with comprehensive analysis
        
        Args:
            portfolios: List of optimized Portfolio objects
            log_returns: DataFrame with log returns for weight direction
            total_lot_size: Total lot size for position sizing
            
        Returns:
            Dictionary with formatted portfolio presentation
        """
        logger.info(f"Presenting {len(portfolios)} portfolios")
        
        if not portfolios:
            logger.warning("No portfolios provided for presentation")
            return self._create_empty_presentation()
        
        # Ensure we have at least MIN_PORTFOLIOS
        if len(portfolios) < MIN_PORTFOLIOS:
            logger.warning(f"Only {len(portfolios)} portfolios provided, minimum is {MIN_PORTFOLIOS}")
        
        # Process each portfolio
        processed_portfolios = []
        
        for i, portfolio in enumerate(portfolios[:MIN_PORTFOLIOS * 2]):  # Process up to 2x minimum
            try:
                processed = self._process_portfolio(portfolio, log_returns, total_lot_size, i + 1)
                if processed:
                    processed_portfolios.append(processed)
            except Exception as e:
                logger.error(f"Error processing portfolio {i+1}: {str(e)}")
        
        # Select best portfolios for presentation
        final_portfolios = self._select_presentation_portfolios(processed_portfolios)
        
        # Create comprehensive presentation
        presentation = self._create_presentation(final_portfolios, log_returns)
        
        # Store in history
        self.presentation_history.append({
            'timestamp': datetime.now(),
            'portfolios': final_portfolios,
            'summary': presentation['summary']
        })
        
        logger.info(f"Portfolio presentation complete: {len(final_portfolios)} portfolios")
        return presentation
    
    def _process_portfolio(self, 
                         portfolio: Portfolio,
                         log_returns: pd.DataFrame,
                         total_lot_size: float,
                         index: int) -> Optional[Dict]:
        """
        Process a single portfolio for presentation
        
        Args:
            portfolio: Portfolio object to process
            log_returns: DataFrame with log returns
            total_lot_size: Total lot size
            index: Portfolio index
            
        Returns:
            Processed portfolio dictionary or None if failed
        """
        try:
            # Validate portfolio
            is_valid, validation_message = self._validate_portfolio_for_presentation(portfolio)
            if not is_valid:
                logger.warning(f"Portfolio {index} validation failed: {validation_message}")
                return None
            
            # Get base weights
            base_weights = portfolio.get_weight_dict()
            
            # Assign weight directions based on log returns
            directional_weights = self.weight_manager.assign_weight_directions(
                base_weights, log_returns
            )
            
            # Normalize weights to ensure absolute sum = 1
            normalized_weights = self.weight_manager.normalize_absolute_weights(directional_weights)
            
            # Calculate lot sizes
            lot_sizes = self.weight_manager.calculate_lot_sizes(
                normalized_weights, total_lot_size
            )
            
            # Calculate weight statistics
            weight_stats = self.weight_manager.get_weight_statistics(normalized_weights)
            
            # Enhanced risk analysis
            portfolio_risk = self.risk_calculator.calculate_portfolio_risk(
                log_returns, normalized_weights
            )
            
            # Create processed portfolio
            processed = {
                'index': index,
                'strategy': portfolio.strategy,
                'pairs': portfolio.pairs,
                'original_weights': base_weights,
                'directional_weights': normalized_weights,
                'lot_sizes': lot_sizes,
                'metrics': portfolio.metrics,
                'risk_profile': portfolio.risk_profile,
                'portfolio_risk': portfolio_risk,
                'weight_statistics': weight_stats,
                'validation': {
                    'is_valid': True,
                    'message': validation_message
                },
                'currency_distribution': self._analyze_currency_distribution(portfolio.pairs),
                'position_summary': self._create_position_summary(normalized_weights, lot_sizes)
            }
            
            return processed
            
        except Exception as e:
            logger.error(f"Error processing portfolio {index}: {str(e)}")
            return None
    
    def _validate_portfolio_for_presentation(self, portfolio: Portfolio) -> Tuple[bool, str]:
        """Validate portfolio for presentation"""
        # Check basic portfolio structure
        if not portfolio.pairs or not portfolio.weights:
            return False, "Portfolio missing pairs or weights"
        
        if len(portfolio.pairs) != PORTFOLIO_SIZE:
            return False, f"Portfolio must have exactly {PORTFOLIO_SIZE} pairs"
        
        if len(portfolio.pairs) != len(portfolio.weights):
            return False, "Mismatch between pairs and weights count"
        
        # Check currency distribution
        is_valid, message = validate_portfolio_distribution(portfolio.pairs)
        if not is_valid:
            return False, f"Currency distribution violation: {message}"
        
        # Check for duplicate pairs
        if len(set(portfolio.pairs)) != len(portfolio.pairs):
            return False, "Portfolio contains duplicate pairs"
        
        return True, "Portfolio is valid for presentation"
    
    def _analyze_currency_distribution(self, pairs: List[str]) -> Dict[str, int]:
        """Analyze currency distribution in portfolio"""
        base_counts = {}
        quote_counts = {}
        
        for pair in pairs:
            if len(pair) >= 6:
                base = pair[:3]
                quote = pair[3:6]
                
                base_counts[base] = base_counts.get(base, 0) + 1
                quote_counts[quote] = quote_counts.get(quote, 0) + 1
        
        return {
            'base_currencies': base_counts,
            'quote_currencies': quote_counts,
            'total_currencies': len(set(list(base_counts.keys()) + list(quote_counts.keys()))),
            'max_base_occurrence': max(base_counts.values()) if base_counts else 0,
            'max_quote_occurrence': max(quote_counts.values()) if quote_counts else 0
        }
    
    def _create_position_summary(self, 
                               weights: Dict[str, float],
                               lot_sizes: Dict[str, float]) -> Dict:
        """Create position summary"""
        long_positions = {pair: weight for pair, weight in weights.items() if weight > 0}
        short_positions = {pair: weight for pair, weight in weights.items() if weight < 0}
        
        long_lots = sum(lot_sizes[pair] for pair in long_positions.keys())
        short_lots = sum(abs(lot_sizes[pair]) for pair in short_positions.keys())
        
        return {
            'long_positions': long_positions,
            'short_positions': short_positions,
            'num_long': len(long_positions),
            'num_short': len(short_positions),
            'long_weight_sum': sum(long_positions.values()),
            'short_weight_sum': sum(abs(w) for w in short_positions.values()),
            'long_lots_sum': long_lots,
            'short_lots_sum': short_lots,
            'net_lots': long_lots - short_lots
        }
    
    def _select_presentation_portfolios(self, 
                                      processed_portfolios: List[Dict]) -> List[Dict]:
        """
        Select the best portfolios for presentation
        
        Args:
            processed_portfolios: List of processed portfolio dictionaries
            
        Returns:
            List of selected portfolios for presentation
        """
        if not processed_portfolios:
            return []
        
        # Group by strategy
        strategy_groups = {}
        for portfolio in processed_portfolios:
            strategy = portfolio['strategy']
            if strategy not in strategy_groups:
                strategy_groups[strategy] = []
            strategy_groups[strategy].append(portfolio)
        
        # Select best from each strategy
        selected = []
        
        # Priority order for strategies
        strategy_priority = ['max_sharpe', 'max_sortino', 'min_variance']
        
        for strategy in strategy_priority:
            if strategy in strategy_groups:
                # Sort by primary metric for the strategy
                if strategy == 'max_sharpe':
                    strategy_groups[strategy].sort(
                        key=lambda p: p['metrics'].get('sharpe_ratio', -np.inf), reverse=True
                    )
                elif strategy == 'max_sortino':
                    strategy_groups[strategy].sort(
                        key=lambda p: p['metrics'].get('sortino_ratio', -np.inf), reverse=True
                    )
                elif strategy == 'min_variance':
                    strategy_groups[strategy].sort(
                        key=lambda p: p['metrics'].get('volatility', np.inf)
                    )
                
                # Add best portfolio from this strategy
                if strategy_groups[strategy]:
                    selected.append(strategy_groups[strategy][0])
        
        # If we don't have enough portfolios, add more from the best performing strategy
        if len(selected) < MIN_PORTFOLIOS:
            all_portfolios = [p for group in strategy_groups.values() for p in group]
            all_portfolios.sort(key=lambda p: p['metrics'].get('sharpe_ratio', -np.inf), reverse=True)
            
            for portfolio in all_portfolios:
                if portfolio not in selected:
                    selected.append(portfolio)
                    if len(selected) >= MIN_PORTFOLIOS:
                        break
        
        return selected[:MIN_PORTFOLIOS * 2]  # Return up to 2x minimum for variety
    
    def _create_presentation(self, 
                           portfolios: List[Dict],
                           log_returns: pd.DataFrame) -> Dict:
        """
        Create comprehensive portfolio presentation
        
        Args:
            portfolios: List of processed portfolios
            log_returns: DataFrame with log returns
            
        Returns:
            Complete presentation dictionary
        """
        if not portfolios:
            return self._create_empty_presentation()
        
        # Calculate summary statistics
        summary = self._calculate_presentation_summary(portfolios)
        
        # Create comparison tables
        comparison_table = self._create_comparison_table(portfolios)
        
        # Risk analysis
        risk_analysis = self._create_risk_analysis(portfolios)
        
        # Currency exposure analysis
        currency_exposure = self._analyze_currency_exposure(portfolios)
        
        # Performance ranking
        performance_ranking = self._create_performance_ranking(portfolios)
        
        presentation = {
            'timestamp': datetime.now().isoformat(),
            'num_portfolios': len(portfolios),
            'portfolios': portfolios,
            'summary': summary,
            'comparison_table': comparison_table,
            'risk_analysis': risk_analysis,
            'currency_exposure': currency_exposure,
            'performance_ranking': performance_ranking,
            'recommendations': self._generate_recommendations(portfolios)
        }
        
        return presentation
    
    def _calculate_presentation_summary(self, portfolios: List[Dict]) -> Dict:
        """Calculate summary statistics across all portfolios"""
        if not portfolios:
            return {}
        
        # Extract metrics
        sharpe_ratios = [p['metrics'].get('sharpe_ratio', 0) for p in portfolios]
        volatilities = [p['metrics'].get('volatility', 0) for p in portfolios]
        returns = [p['metrics'].get('expected_return', 0) for p in portfolios]
        
        # Strategy distribution
        strategies = [p['strategy'] for p in portfolios]
        strategy_counts = {strategy: strategies.count(strategy) for strategy in set(strategies)}
        
        # Position statistics
        total_long = sum(p['position_summary']['num_long'] for p in portfolios)
        total_short = sum(p['position_summary']['num_short'] for p in portfolios)
        
        summary = {
            'best_sharpe': max(sharpe_ratios) if sharpe_ratios else 0,
            'avg_sharpe': np.mean(sharpe_ratios) if sharpe_ratios else 0,
            'min_volatility': min(volatilities) if volatilities else 0,
            'avg_volatility': np.mean(volatilities) if volatilities else 0,
            'max_return': max(returns) if returns else 0,
            'avg_return': np.mean(returns) if returns else 0,
            'strategy_distribution': strategy_counts,
            'total_long_positions': total_long,
            'total_short_positions': total_short,
            'avg_long_per_portfolio': total_long / len(portfolios) if portfolios else 0,
            'avg_short_per_portfolio': total_short / len(portfolios) if portfolios else 0
        }
        
        return summary
    
    def _create_comparison_table(self, portfolios: List[Dict]) -> pd.DataFrame:
        """Create portfolio comparison table"""
        if not portfolios:
            return pd.DataFrame()
        
        comparison_data = []
        
        for portfolio in portfolios:
            metrics = portfolio['metrics']
            weight_stats = portfolio['weight_statistics']
            
            row = {
                'Portfolio': f"Portfolio {portfolio['index']}",
                'Strategy': portfolio['strategy'].replace('_', ' ').title(),
                'Sharpe Ratio': metrics.get('sharpe_ratio', 0),
                'Sortino Ratio': metrics.get('sortino_ratio', 0),
                'Calmar Ratio': metrics.get('calmar_ratio', 0),
                'Expected Return': metrics.get('expected_return', 0),
                'Volatility': metrics.get('volatility', 0),
                'Max Drawdown': metrics.get('max_drawdown', 0),
                'Long Positions': weight_stats.get('long_positions', 0),
                'Short Positions': weight_stats.get('short_positions', 0),
                'Max Weight': weight_stats.get('max_abs_weight', 0),
                'Weight Concentration': weight_stats.get('concentration_hhi', 0)
            }
            
            comparison_data.append(row)
        
        return pd.DataFrame(comparison_data)
    
    def _create_risk_analysis(self, portfolios: List[Dict]) -> Dict:
        """Create comprehensive risk analysis"""
        if not portfolios:
            return {}
        
        # Risk level distribution
        risk_levels = []
        for portfolio in portfolios:
            portfolio_risk = portfolio.get('portfolio_risk', {})
            risk_level = portfolio_risk.get('risk_level', 'UNKNOWN')
            risk_levels.append(risk_level)
        
        risk_distribution = {level: risk_levels.count(level) for level in set(risk_levels)}
        
        # Correlation analysis
        avg_correlations = []
        for portfolio in portfolios:
            portfolio_risk = portfolio.get('portfolio_risk', {})
            avg_corr = portfolio_risk.get('avg_correlation', 0)
            avg_correlations.append(avg_corr)
        
        risk_analysis = {
            'risk_level_distribution': risk_distribution,
            'avg_portfolio_correlation': np.mean(avg_correlations) if avg_correlations else 0,
            'max_portfolio_correlation': max(avg_correlations) if avg_correlations else 0,
            'min_portfolio_correlation': min(avg_correlations) if avg_correlations else 0,
            'diversification_scores': [
                p.get('portfolio_risk', {}).get('diversification_ratio', 1.0) 
                for p in portfolios
            ]
        }
        
        return risk_analysis
    
    def _analyze_currency_exposure(self, portfolios: List[Dict]) -> Dict:
        """Analyze currency exposure across portfolios"""
        all_currencies = set()
        currency_frequency = {}
        
        for portfolio in portfolios:
            dist = portfolio['currency_distribution']
            
            # Collect all currencies
            for currency in dist['base_currencies'].keys():
                all_currencies.add(currency)
                currency_frequency[currency] = currency_frequency.get(currency, 0) + 1
            
            for currency in dist['quote_currencies'].keys():
                all_currencies.add(currency)
                currency_frequency[currency] = currency_frequency.get(currency, 0) + 1
        
        return {
            'total_currencies_used': len(all_currencies),
            'currency_frequency': currency_frequency,
            'most_used_currency': max(currency_frequency.items(), key=lambda x: x[1]) if currency_frequency else None,
            'least_used_currency': min(currency_frequency.items(), key=lambda x: x[1]) if currency_frequency else None
        }
    
    def _create_performance_ranking(self, portfolios: List[Dict]) -> List[Dict]:
        """Create performance ranking of portfolios"""
        if not portfolios:
            return []
        
        # Create ranking based on multiple criteria
        rankings = []
        
        for portfolio in portfolios:
            metrics = portfolio['metrics']
            
            # Calculate composite score
            sharpe_score = metrics.get('sharpe_ratio', 0) * 0.4
            sortino_score = metrics.get('sortino_ratio', 0) * 0.3
            calmar_score = metrics.get('calmar_ratio', 0) * 0.2
            return_score = metrics.get('expected_return', 0) * 1000 * 0.1  # Scale return
            
            composite_score = sharpe_score + sortino_score + calmar_score + return_score
            
            rankings.append({
                'portfolio_index': portfolio['index'],
                'strategy': portfolio['strategy'],
                'composite_score': composite_score,
                'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                'sortino_ratio': metrics.get('sortino_ratio', 0),
                'calmar_ratio': metrics.get('calmar_ratio', 0),
                'expected_return': metrics.get('expected_return', 0)
            })
        
        # Sort by composite score
        rankings.sort(key=lambda x: x['composite_score'], reverse=True)
        
        # Add rank
        for i, ranking in enumerate(rankings):
            ranking['rank'] = i + 1
        
        return rankings
    
    def _generate_recommendations(self, portfolios: List[Dict]) -> List[str]:
        """Generate recommendations based on portfolio analysis"""
        recommendations = []
        
        if not portfolios:
            recommendations.append("No portfolios available for analysis.")
            return recommendations
        
        # Analyze performance
        sharpe_ratios = [p['metrics'].get('sharpe_ratio', 0) for p in portfolios]
        best_sharpe = max(sharpe_ratios) if sharpe_ratios else 0
        
        if best_sharpe > 1.5:
            recommendations.append("Excellent risk-adjusted returns detected. Consider the highest Sharpe ratio portfolio for aggressive growth.")
        elif best_sharpe > 1.0:
            recommendations.append("Good risk-adjusted returns available. Balanced approach recommended.")
        else:
            recommendations.append("Conservative approach advised due to moderate risk-adjusted returns.")
        
        # Analyze diversification
        avg_correlations = [p.get('portfolio_risk', {}).get('avg_correlation', 0) for p in portfolios]
        avg_correlation = np.mean(avg_correlations) if avg_correlations else 0
        
        if avg_correlation < 0.3:
            recommendations.append("Excellent diversification achieved across portfolios.")
        elif avg_correlation < 0.6:
            recommendations.append("Good diversification present. Monitor correlation changes.")
        else:
            recommendations.append("High correlation detected. Consider more diverse currency pairs.")
        
        # Strategy recommendations
        strategies = [p['strategy'] for p in portfolios]
        if 'max_sharpe' in strategies:
            recommendations.append("Sharpe-optimized portfolio available for risk-adjusted growth.")
        if 'min_variance' in strategies:
            recommendations.append("Low-volatility portfolio available for conservative investors.")
        
        return recommendations
    
    def _create_empty_presentation(self) -> Dict:
        """Create empty presentation structure"""
        return {
            'timestamp': datetime.now().isoformat(),
            'num_portfolios': 0,
            'portfolios': [],
            'summary': {},
            'comparison_table': pd.DataFrame(),
            'risk_analysis': {},
            'currency_exposure': {},
            'performance_ranking': [],
            'recommendations': ["No portfolios available for presentation."]
        }
    
    def get_presentation_history(self) -> List[Dict]:
        """Get historical presentations"""
        return self.presentation_history.copy()
    
    def export_presentation(self, presentation: Dict, format: str = 'json') -> str:
        """Export presentation to specified format"""
        if format == 'json':
            import json
            return json.dumps(presentation, indent=2, default=str)
        elif format == 'csv':
            if 'comparison_table' in presentation and not presentation['comparison_table'].empty:
                return presentation['comparison_table'].to_csv(index=False)
            else:
                return "No comparison table available for CSV export."
        else:
            raise ValueError(f"Unsupported export format: {format}")


if __name__ == "__main__":
    # Test the portfolio presenter
    logging.basicConfig(level=logging.INFO)
    
    print("Testing Portfolio Presenter...")
    
    # This would normally use real Portfolio objects and data
    # For testing, we'll create a simple test
    presenter = PortfolioPresenter()
    
    # Create empty presentation
    empty_presentation = presenter._create_empty_presentation()
    print(f"✓ Empty presentation created: {len(empty_presentation)} sections")
    
    # Test export
    json_export = presenter.export_presentation(empty_presentation, 'json')
    print(f"✓ JSON export: {len(json_export)} characters")
    
    print("Portfolio Presenter test complete.")
