#!/usr/bin/env python3
"""
Test script to demonstrate dynamic width feature with more volatile data.
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from dispersion_charts import DispersionChartCreator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_volatile_test_data():
    """Create test data with periods of high volatility to demonstrate dynamic width."""
    
    # Create time index (24 hours of minute data)
    start_time = datetime.now() - timedelta(hours=24)
    time_index = pd.date_range(start=start_time, periods=1440, freq='1min')
    
    # Currency pairs
    pairs = [
        'EURUSD', 'EURGBP', 'EURAUD', 'EURNZD', 'EURCHF', 'EURCAD', 'EURJPY',
        'GBPUSD', 'GBPAUD', 'GBPNZD', 'GBPCHF', 'GBPCAD', 'GBPJPY',
        'AUDUSD', 'AUDNZD', 'AUDCHF', 'AUDCAD', 'AUDJPY',
        'NZDUSD', 'NZDCHF', 'NZDCAD', 'NZDJPY',
        'USDCHF', 'USDCAD', 'USDJPY',
        'CADCHF', 'CADJPY', 'CHFJPY'
    ]
    
    # Create base random data
    np.random.seed(42)
    base_data = np.random.normal(0, 0.001, (len(time_index), len(pairs)))
    
    # Add periods of high volatility to make dynamic width visible
    volatility_periods = [
        (200, 250),   # Period 1: High volatility
        (400, 420),   # Period 2: Short spike
        (600, 680),   # Period 3: Extended volatility
        (900, 930),   # Period 4: Another spike
        (1200, 1280), # Period 5: End volatility
    ]
    
    for start_idx, end_idx in volatility_periods:
        if end_idx < len(time_index):
            # Increase volatility by 5x during these periods
            base_data[start_idx:end_idx] *= 5
            
            # Add some trending behavior to create ROC spikes
            trend = np.linspace(0, 0.01, end_idx - start_idx)
            for i, pair in enumerate(pairs):
                if 'USD' in pair:
                    # USD pairs get positive trend
                    base_data[start_idx:end_idx, i] += trend
                elif 'EUR' in pair:
                    # EUR pairs get negative trend
                    base_data[start_idx:end_idx, i] -= trend
                elif 'GBP' in pair:
                    # GBP pairs get oscillating pattern
                    oscillation = 0.005 * np.sin(np.linspace(0, 4*np.pi, end_idx - start_idx))
                    base_data[start_idx:end_idx, i] += oscillation
    
    # Create DataFrame
    df = pd.DataFrame(base_data, index=time_index, columns=pairs)
    
    logger.info(f"Created volatile test data with shape: {df.shape}")
    logger.info(f"Added {len(volatility_periods)} high volatility periods")
    
    return df

def main():
    logger.info("Starting Dynamic Width Test...")
    
    # Create volatile test data
    test_data = create_volatile_test_data()
    
    # Initialize dispersion charts
    charts = DispersionChartCreator()
    
    # Create currency CSSD chart
    logger.info("Creating currency CSSD chart with dynamic width...")
    fig, pair_contributions = charts.create_currency_cssd_chart(test_data)
    
    # Count traces
    trace_count = len(fig.data)
    logger.info(f"Chart created with {trace_count} traces")
    
    # Count thick vs normal segments
    thick_segments = 0
    normal_segments = 0
    
    for trace in fig.data:
        if trace.line and hasattr(trace.line, 'width'):
            if trace.line.width == 12:  # Thick segments
                thick_segments += 1
            elif trace.line.width == 2:  # Normal segments
                normal_segments += 1
    
    logger.info(f"Thick segments (width=12): {thick_segments}")
    logger.info(f"Normal segments (width=2): {normal_segments}")
    
    # Show some trace details
    logger.info("Sample traces:")
    for i, trace in enumerate(fig.data[:10]):  # Show first 10 traces
        width = trace.line.width if trace.line and hasattr(trace.line, 'width') else 'N/A'
        logger.info(f"Trace {i}: {trace.name} - Width: {width}")
    
    # Save chart for inspection
    try:
        fig.write_html("dynamic_width_test.html")
        logger.info("Chart saved as 'dynamic_width_test.html'")
    except Exception as e:
        logger.warning(f"Could not save chart: {e}")
    
    logger.info("✅ Dynamic width test completed!")
    
    if thick_segments > 0:
        logger.info(f"✅ Dynamic width feature is working! Found {thick_segments} thick segments.")
    else:
        logger.warning("⚠️ No thick segments found. May need to adjust ROC sensitivity.")

if __name__ == "__main__":
    main()
