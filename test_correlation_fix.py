#!/usr/bin/env python3
"""
Test script to verify correlation calculation with sign mapping
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from dispersion_charts import DispersionChartCreator

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_usd_correlation():
    """
    Test USD correlation with proper sign mapping
    """
    # Create simple test data
    time_index = pd.date_range(start=datetime.now(), periods=100, freq='1min')
    
    # USD pairs that should be correlated when USD moves
    usd_pairs = ['EURUSD', 'GBPUSD', 'USDCAD', 'USDCHF', 'USDJPY']
    
    # Create coordinated USD movement
    np.random.seed(42)
    base_data = np.random.normal(0, 0.0001, (len(time_index), len(usd_pairs)))
    
    # Add strong USD movement at time 50-60
    usd_strength = 0.005  # Strong USD movement
    for i in range(50, 60):
        for j, pair in enumerate(usd_pairs):
            if pair.startswith('USD'):
                # USD is base: USD strength = pair goes up
                base_data[i, j] = usd_strength
            else:
                # USD is quote: USD strength = pair goes down
                base_data[i, j] = -usd_strength
    
    # Create DataFrame
    df = pd.DataFrame(base_data, index=time_index, columns=usd_pairs)
    
    # Test the correlation calculation
    charts = DispersionChartCreator()
    
    # Get sign map for USD
    sign_map = charts._get_currency_sign_map('USD', usd_pairs)
    print(f"USD Sign Map: {sign_map}")
    
    # Apply sign mapping
    currency_returns = df.diff()
    for pair in usd_pairs:
        if pair in sign_map:
            currency_returns[pair] = currency_returns[pair] * sign_map[pair]
    
    # Calculate correlation for the movement period
    movement_returns = currency_returns.iloc[50:60]
    corr_matrix = movement_returns.corr()
    
    print("\nCorrelation Matrix (after sign mapping):")
    print(corr_matrix)
    
    # Count high correlations
    high_corr_count = 0
    for j in range(len(usd_pairs)):
        for k in range(j+1, len(usd_pairs)):
            corr_val = corr_matrix.iloc[j, k]
            if not pd.isna(corr_val) and corr_val > 0.4:
                high_corr_count += 1
                print(f"High correlation: {usd_pairs[j]} vs {usd_pairs[k]} = {corr_val:.3f}")
    
    print(f"\nTotal high correlations: {high_corr_count}")
    
    # Test the actual signal detection
    print("\n" + "="*50)
    print("Testing actual signal detection...")
    
    # Create full dataset for signal detection
    all_pairs = [
        'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDCHF', 'USDJPY',
        'EURGBP', 'EURAUD', 'EURNZD', 'EURCAD', 'EURCHF', 'EURJPY',
        'GBPAUD', 'GBPNZD', 'GBPCAD', 'GBPCHF', 'GBPJPY',
        'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY',
        'NZDCAD', 'NZDCHF', 'NZDJPY',
        'CADCHF', 'CADJPY',
        'CHFJPY'
    ]
    
    # Create full dataset with USD movement
    full_data = np.random.normal(0, 0.0001, (len(time_index), len(all_pairs)))
    
    # Add USD movement to all USD pairs
    for i in range(50, 60):
        for j, pair in enumerate(all_pairs):
            if 'USD' in pair:
                if pair.startswith('USD'):
                    # USD is base: USD strength = pair goes up
                    full_data[i, j] = usd_strength
                else:
                    # USD is quote: USD strength = pair goes down
                    full_data[i, j] = -usd_strength
    
    full_df = pd.DataFrame(full_data, index=time_index, columns=all_pairs)
    
    # Create currency CSSD chart to trigger signal detection
    fig, _ = charts.create_currency_cssd_chart(full_df)
    
    # Count annotations
    annotation_count = len(fig.layout.annotations) if hasattr(fig.layout, 'annotations') and fig.layout.annotations else 0
    print(f"Stars detected: {annotation_count}")

if __name__ == "__main__":
    test_usd_correlation()
