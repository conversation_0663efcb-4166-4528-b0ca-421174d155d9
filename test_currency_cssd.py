"""
Test script for Currency CSSD Chart functionality
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_currency_cssd():
    """Test the currency CSSD calculation and chart creation"""
    
    # Import required modules
    from dispersion_charts import DispersionChartCreator
    from config import CURRENCY_PAIRS
    
    # Create test data
    logger.info("Creating test data...")
    
    # Generate 100 time points for today
    start_time = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    time_index = [start_time + timedelta(minutes=i) for i in range(100)]
    
    # Create normalized returns data for all 28 currency pairs
    np.random.seed(42)  # For reproducible results
    
    # Generate correlated returns to simulate realistic currency behavior
    normalized_returns_data = {}
    
    for pair in CURRENCY_PAIRS:
        # Generate some realistic-looking normalized returns
        base_trend = np.random.normal(0, 0.01, 100)  # Base trend
        noise = np.random.normal(0, 0.005, 100)      # Random noise
        returns = np.cumsum(base_trend + noise)       # Cumulative returns
        normalized_returns_data[pair] = returns
    
    # Create DataFrame
    normalized_returns_df = pd.DataFrame(normalized_returns_data, index=time_index)
    
    logger.info(f"Created test data with shape: {normalized_returns_df.shape}")
    logger.info(f"Currency pairs: {list(normalized_returns_df.columns)}")
    
    # Test currency basket creation
    chart_creator = DispersionChartCreator()
    
    logger.info("Testing currency basket creation...")
    baskets = chart_creator._build_currency_baskets()
    
    for currency, pairs in baskets.items():
        logger.info(f"{currency}: {len(pairs)} pairs - {pairs}")
    
    # Test sign map creation
    logger.info("Testing sign map creation...")
    for currency in ['USD', 'EUR', 'GBP']:
        pairs = baskets[currency]
        sign_map = chart_creator._get_currency_sign_map(currency, pairs)
        logger.info(f"{currency} sign map: {sign_map}")
    
    # Test CSSD calculation and rolling dispersion for each currency
    logger.info("Testing CSSD calculation and rolling dispersion...")
    for currency in ['USD', 'EUR', 'GBP', 'AUD', 'NZD', 'CAD', 'CHF', 'JPY']:
        pairs = baskets[currency]
        sign_map = chart_creator._get_currency_sign_map(currency, pairs)
        cssd_series, mu_t_series, currency_returns = chart_creator._calculate_currency_cssd(
            normalized_returns_df, currency, pairs, sign_map
        )

        if not cssd_series.empty:
            # Calculate rolling dispersion of CSSD
            rolling_cssd_dispersion = cssd_series.groupby(cssd_series.index.date).expanding().std(ddof=1).reset_index(level=0, drop=True)

            # Calculate rolling IQR of CSSD
            rolling_cssd_q75 = cssd_series.groupby(cssd_series.index.date).expanding().quantile(0.75).reset_index(level=0, drop=True)
            rolling_cssd_q25 = cssd_series.groupby(cssd_series.index.date).expanding().quantile(0.25).reset_index(level=0, drop=True)
            rolling_cssd_iqr = rolling_cssd_q75 - rolling_cssd_q25

            logger.info(f"{currency} CSSD: mean={cssd_series.mean():.4f}, "
                       f"std={cssd_series.std():.4f}, "
                       f"min={cssd_series.min():.4f}, "
                       f"max={cssd_series.max():.4f}")

            if not rolling_cssd_dispersion.empty and not rolling_cssd_dispersion.isna().all():
                logger.info(f"{currency} Rolling CSSD Dispersion: mean={rolling_cssd_dispersion.mean():.4f}, "
                           f"std={rolling_cssd_dispersion.std():.4f}, "
                           f"min={rolling_cssd_dispersion.min():.4f}, "
                           f"max={rolling_cssd_dispersion.max():.4f}")
            else:
                logger.warning(f"{currency} Rolling CSSD Dispersion calculation failed")

            if not rolling_cssd_iqr.empty and not rolling_cssd_iqr.isna().all():
                logger.info(f"{currency} Rolling CSSD IQR: mean={rolling_cssd_iqr.mean():.4f}, "
                           f"std={rolling_cssd_iqr.std():.4f}, "
                           f"min={rolling_cssd_iqr.min():.4f}, "
                           f"max={rolling_cssd_iqr.max():.4f}")
            else:
                logger.warning(f"{currency} Rolling CSSD IQR calculation failed")
        else:
            logger.warning(f"{currency} CSSD calculation failed")
    
    # Test chart creation
    logger.info("Testing chart creation...")
    try:
        fig, pair_contributions = chart_creator.create_currency_cssd_chart(normalized_returns_df)
        logger.info(f"Chart created successfully with {len(fig.data)} traces")

        # Print trace names
        for trace in fig.data:
            logger.info(f"Trace: {trace.name}")

        # Print pair contributions
        logger.info("Pair contributions at last timestamp:")
        for currency, contributions in pair_contributions.items():
            if contributions:
                logger.info(f"{currency}: {contributions[:3]}")  # Show top 3

        return True
        
    except Exception as e:
        logger.error(f"Chart creation failed: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting Currency CSSD Test...")
    success = test_currency_cssd()
    
    if success:
        logger.info("✅ All tests passed!")
    else:
        logger.error("❌ Tests failed!")
