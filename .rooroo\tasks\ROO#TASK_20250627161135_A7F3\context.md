# Task Context: Add Close All Trades Button

## Goal
Add functionality to close all trades on MT5 with one button

## Project Overview
This is a Python-based MT5 trading application with a web dashboard interface.

## Key Files
- [`mt5_connector.py`](mt5_connector.py) - MT5 connection and trading operations
- [`dashboard.py`](dashboard.py) - Web dashboard UI
- [`app.py`](app.py) - Main Flask application
- [`assets/style.css`](assets/style.css) - UI styling

## Requirements
1. Add a "Close All Trades" button to the dashboard UI
2. Implement the close all trades functionality in the MT5 connector
3. Connect the button to trigger the close all trades operation
4. Ensure proper error handling and user feedback

## Technical Considerations
- Use existing MT5 connection patterns from the codebase
- Follow existing UI/UX patterns for button placement and styling
- Implement proper confirmation dialog before executing close all trades
- Add appropriate logging and error handling