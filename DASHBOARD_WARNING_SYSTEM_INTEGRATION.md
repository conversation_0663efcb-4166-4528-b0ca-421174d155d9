# Portfolio Entry Warning System - Dashboard Integration

## Overview

The Portfolio Entry Warning System has been successfully integrated into the main dashboard at the bottom of the dispersion analysis section. This system implements the complete 6-step signal detection process for the Dispersion Breakout Portfolio Strategy.

## Location in Dashboard

The warning system appears at the bottom of the dashboard, after the existing dispersion charts:
1. Rolling Dispersion of Normalized Returns CSSD
2. Dispersion with Retracement  
3. **Portfolio Entry Warning System** ← NEW

## Features

### Real-time Signal Detection
- **CSSD Threshold Detection**: Monitors when dispersion exceeds historical percentile thresholds
- **ROC Filter**: Validates rapid dispersion changes (ΔCSSD ≥ 2σ within 15 minutes)
- **Persistence Check**: Ensures signals persist for minimum duration (3 bars)
- **Pre-quiet Validation**: Confirms market was quiet before breakout (60-minute window)
- **Trend Confirmation**: Uses ADX indicator to confirm trending conditions (>25 and rising)
- **News Filter**: Placeholder for future news event filtering

### Interactive Controls

The warning system includes the following configurable parameters:

1. **Lookback Window (min)**: Historical data window for threshold calculation (default: 240 minutes)
2. **Threshold %ile**: Percentile threshold for CSSD breakout detection (default: 95.0%)
3. **ROC Sigma**: Sigma multiplier for rate-of-change validation (default: 2.0)
4. **ADX Threshold**: Minimum ADX value for trend confirmation (default: 25.0)
5. **Update Warning System**: Button to manually refresh the chart with new parameters

### Chart Components

The warning system chart displays three subplots:

1. **Main CSSD Plot**: 
   - CSSD time series with threshold lines
   - Signal markers for qualified breakouts
   - Entry window indicators (30-minute windows)

2. **ROC Subplot**:
   - Rate of change of CSSD
   - Sigma bands for validation
   - ROC signal markers

3. **ADX Subplot**:
   - ADX trend strength indicator
   - Threshold line at configured level
   - Rising/falling trend indicators

## Usage Instructions

### Starting the Dashboard

1. Run the dashboard:
   ```bash
   python dashboard.py
   ```

2. Open your browser and navigate to the dashboard URL (typically `http://localhost:8050`)

3. Scroll down to the bottom of the page to find the "Portfolio Entry Warning System" section

### Using the Warning System

1. **Automatic Updates**: The chart updates automatically every minute along with other dispersion charts

2. **Manual Updates**: Click the "Update Warning System" button to refresh with current parameters

3. **Parameter Adjustment**: 
   - Modify any of the input parameters
   - Click "Update Warning System" to apply changes
   - Parameters are validated and defaults are used if invalid values are provided

4. **Signal Interpretation**:
   - **Green markers**: Qualified entry signals
   - **Red zones**: 30-minute entry windows following signals
   - **Blue lines**: CSSD threshold levels
   - **Orange markers**: ROC validation points
   - **Purple line**: ADX trend strength

### Signal Validation Process

When a signal is detected, the system validates it through all 6 steps:

1. ✅ **CSSD > Threshold**: Current dispersion exceeds historical percentile
2. ✅ **ROC Validation**: Rate of change meets sigma requirements
3. ✅ **Persistence**: Signal persists for minimum duration
4. ✅ **Pre-quiet Check**: Market was quiet before breakout
5. ✅ **Trend Confirmation**: ADX indicates trending conditions
6. ✅ **News Filter**: No conflicting news events (placeholder)

Only signals passing all 6 steps are marked as qualified entry opportunities.

## Technical Implementation

### Data Flow
1. Market data fetched from MT5 connector
2. Log returns calculated and normalized by realized volatility
3. CSSD dispersion metric computed
4. 6-step signal detection algorithm applied
5. Chart generated with all signal components
6. Status information displayed below chart

### Performance Considerations
- Chart updates every minute with dispersion interval
- Historical lookback window affects calculation time
- Larger datasets may require optimization for real-time performance

### Error Handling
- Graceful degradation when market data unavailable
- Default parameter values used for invalid inputs
- Status messages indicate data availability and errors
- Empty chart displayed with informative message when no data

## Integration with Trading Strategy

The warning system is designed to support the complete trading workflow:

1. **Signal Detection**: Automated identification of qualified breakout opportunities
2. **Entry Timing**: 30-minute entry windows clearly marked
3. **Risk Assessment**: Integration with existing portfolio optimization
4. **Position Management**: Coordinates with MT5 trading interface

## Troubleshooting

### Common Issues

1. **No Data Available**: 
   - Check MT5 connection
   - Verify market hours
   - Ensure sufficient historical data

2. **Chart Not Updating**:
   - Check dispersion interval is running
   - Verify no JavaScript errors in browser console
   - Refresh the page

3. **Parameter Validation**:
   - Ensure all parameters are within valid ranges
   - Use default values if unsure
   - Check status message for specific errors

### Support

For technical issues or questions about the warning system:
1. Check the status message below the chart
2. Review browser console for JavaScript errors
3. Check Python logs for backend errors
4. Verify all required modules are properly installed

## Future Enhancements

Planned improvements include:
- News event integration for step 6 filtering
- Machine learning signal validation
- Portfolio size optimization based on signal strength
- Historical backtesting integration
- Mobile-responsive design improvements
