"""
Test Script for Matrix QP Application
Comprehensive testing of all modules and functionality
"""

import sys
import os
import logging
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import all modules
from config import CURRENCY_PAIRS
from mt5_connector import MT5Connector
from log_returns import LogReturnsCalculator, calculate_returns_from_data
from risk_calculator import RiskCalculator
from portfolio_optimizer import PortfolioOptimizer, Portfolio
from weight_manager import WeightManager
from portfolio_presenter import PortfolioPresenter
from utils import PerformanceTimer, validate_currency_pair, format_number
from app import MatrixQPApplication

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class MatrixQPTester:
    """
    Comprehensive test suite for Matrix QP application
    """
    
    def __init__(self):
        """Initialize the tester"""
        self.test_results = {}
        self.sample_data = None
        self.sample_returns = None
    
    def run_all_tests(self) -> Dict[str, bool]:
        """
        Run all tests and return results
        
        Returns:
            Dictionary with test results
        """
        logger.info("Starting Matrix QP comprehensive test suite...")
        
        # Test order matters - some tests depend on others
        test_methods = [
            self.test_config_module,
            self.test_utils_module,
            self.test_sample_data_generation,
            self.test_log_returns_calculator,
            self.test_risk_calculator,
            self.test_weight_manager,
            self.test_portfolio_optimizer,
            self.test_portfolio_presenter,
            self.test_mt5_connector,
            self.test_integration,
            self.test_application_class
        ]
        
        for test_method in test_methods:
            test_name = test_method.__name__
            logger.info(f"Running {test_name}...")
            
            try:
                with PerformanceTimer(test_name):
                    result = test_method()
                    self.test_results[test_name] = result
                    
                if result:
                    logger.info(f"✓ {test_name} PASSED")
                else:
                    logger.error(f"✗ {test_name} FAILED")
                    
            except Exception as e:
                logger.error(f"✗ {test_name} ERROR: {str(e)}")
                self.test_results[test_name] = False
        
        # Print summary
        self.print_test_summary()
        
        return self.test_results
    
    def test_config_module(self) -> bool:
        """Test configuration module"""
        try:
            # Test currency pairs
            assert len(CURRENCY_PAIRS) == 28, f"Expected 28 pairs, got {len(CURRENCY_PAIRS)}"
            
            # Test currency pair format
            for pair in CURRENCY_PAIRS:
                assert len(pair) == 6, f"Invalid pair format: {pair}"
                assert pair.isupper(), f"Pair not uppercase: {pair}"
            
            # Test utility functions
            from config import get_currency_from_pair, validate_portfolio_distribution
            
            assert get_currency_from_pair("EURUSD", "base") == "EUR"
            assert get_currency_from_pair("EURUSD", "quote") == "USD"
            
            # Test portfolio validation
            test_pairs = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "NZDUSD"]
            is_valid, message = validate_portfolio_distribution(test_pairs)
            assert is_valid, f"Portfolio validation failed: {message}"
            
            return True
            
        except Exception as e:
            logger.error(f"Config module test failed: {str(e)}")
            return False
    
    def test_utils_module(self) -> bool:
        """Test utilities module"""
        try:
            from utils import (
                validate_currency_pair, format_number, safe_divide,
                calculate_percentage_change, clean_dataframe
            )
            
            # Test currency pair validation
            assert validate_currency_pair("EURUSD") == True
            assert validate_currency_pair("INVALID") == False
            
            # Test number formatting
            assert format_number(0.1234, 2) == "0.12"
            assert format_number(0.1234, 2, percentage=True) == "12.34%"
            
            # Test safe division
            assert safe_divide(10, 2) == 5.0
            assert safe_divide(10, 0, default=0.0) == 0.0
            
            # Test percentage change
            assert calculate_percentage_change(100, 110) == 10.0
            
            # Test DataFrame cleaning
            df = pd.DataFrame({'A': [1, 2, np.nan, 4], 'B': [np.inf, 2, 3, 4]})
            cleaned = clean_dataframe(df, drop_na=True)
            assert not cleaned.isnull().any().any()
            
            return True
            
        except Exception as e:
            logger.error(f"Utils module test failed: {str(e)}")
            return False
    
    def test_sample_data_generation(self) -> bool:
        """Generate sample data for testing"""
        try:
            # Create sample market data
            dates = pd.date_range('2024-01-01', periods=500, freq='15T')
            
            self.sample_data = {}
            np.random.seed(42)  # For reproducible results
            
            for pair in CURRENCY_PAIRS[:10]:  # Use first 10 pairs for testing
                # Generate realistic price data
                base_price = 1.0 + np.random.uniform(-0.5, 0.5)
                returns = np.random.normal(0, 0.001, len(dates))
                prices = base_price * np.exp(np.cumsum(returns))
                
                # Create OHLC data
                self.sample_data[pair] = pd.DataFrame({
                    'open': prices * (1 + np.random.normal(0, 0.0001, len(dates))),
                    'high': prices * (1 + np.abs(np.random.normal(0, 0.0002, len(dates)))),
                    'low': prices * (1 - np.abs(np.random.normal(0, 0.0002, len(dates)))),
                    'close': prices,
                    'tick_volume': np.random.randint(100, 1000, len(dates))
                }, index=dates)
            
            assert len(self.sample_data) == 10
            assert all(len(df) == 500 for df in self.sample_data.values())
            
            return True
            
        except Exception as e:
            logger.error(f"Sample data generation failed: {str(e)}")
            return False
    
    def test_log_returns_calculator(self) -> bool:
        """Test log returns calculation"""
        try:
            calculator = LogReturnsCalculator()
            
            # Calculate log returns
            log_returns = calculator.calculate_log_returns(self.sample_data)
            
            assert not log_returns.empty, "Log returns DataFrame is empty"
            assert len(log_returns.columns) == len(self.sample_data)
            
            # Test absolute returns
            abs_returns = calculator.calculate_absolute_returns(log_returns)
            assert not abs_returns.empty, "Absolute returns DataFrame is empty"
            assert (abs_returns >= 0).all().all(), "Absolute returns contain negative values"
            
            # Test statistics
            stats = calculator.get_return_statistics(log_returns)
            assert len(stats) == len(log_returns.columns)
            
            # Test negative return identification
            negative_pairs = calculator.identify_negative_return_pairs(log_returns)
            assert isinstance(negative_pairs, dict)
            
            # Test correlation matrix
            corr_matrix = calculator.get_correlation_matrix(log_returns)
            assert corr_matrix.shape == (len(log_returns.columns), len(log_returns.columns))
            
            # Store for other tests
            self.sample_returns = log_returns
            
            return True
            
        except Exception as e:
            logger.error(f"Log returns calculator test failed: {str(e)}")
            return False
    
    def test_risk_calculator(self) -> bool:
        """Test risk calculation"""
        try:
            calculator = RiskCalculator()
            
            # Calculate risk profiles
            risk_profiles = calculator.calculate_risk_profiles(self.sample_returns)
            
            assert len(risk_profiles) > 0, "No risk profiles calculated"
            
            # Check risk profile structure
            for pair, profile in risk_profiles.items():
                required_metrics = ['sharpe_ratio', 'volatility', 'max_drawdown']
                for metric in required_metrics:
                    assert metric in profile, f"Missing metric {metric} for {pair}"
            
            # Test portfolio risk calculation
            test_weights = {pair: 1.0/len(self.sample_returns.columns) 
                          for pair in self.sample_returns.columns}
            
            portfolio_risk = calculator.calculate_portfolio_risk(self.sample_returns, test_weights)
            assert len(portfolio_risk) > 0, "Portfolio risk calculation failed"
            
            # Test ranking
            rankings = calculator.rank_pairs_by_risk(risk_profiles, 'sharpe_ratio')
            assert len(rankings) == len(risk_profiles)
            
            return True
            
        except Exception as e:
            logger.error(f"Risk calculator test failed: {str(e)}")
            return False
    
    def test_weight_manager(self) -> bool:
        """Test weight management"""
        try:
            manager = WeightManager()
            
            # Test weight normalization
            test_weights = {f"PAIR{i}": np.random.uniform(0.1, 0.3) for i in range(6)}
            normalized = manager.normalize_absolute_weights(test_weights)
            
            abs_sum = sum(abs(w) for w in normalized.values())
            assert abs(abs_sum - 1.0) < 1e-6, f"Weights not normalized: sum = {abs_sum}"
            
            # Test weight direction assignment
            directional = manager.assign_weight_directions(normalized, self.sample_returns)
            assert len(directional) == len(normalized)
            
            # Test lot size calculation
            lot_sizes = manager.calculate_lot_sizes(directional, 10.0)
            total_lots = sum(abs(lot) for lot in lot_sizes.values())
            assert abs(total_lots - 10.0) < 0.1, f"Lot sizes not correct: total = {total_lots}"
            
            # Test weight validation
            is_valid, errors = manager.validate_weights(normalized)
            assert is_valid, f"Weight validation failed: {errors}"
            
            # Test weight statistics
            stats = manager.get_weight_statistics(directional)
            assert 'num_assets' in stats
            assert stats['num_assets'] == len(directional)
            
            return True
            
        except Exception as e:
            logger.error(f"Weight manager test failed: {str(e)}")
            return False
    
    def test_portfolio_optimizer(self) -> bool:
        """Test portfolio optimization"""
        try:
            optimizer = PortfolioOptimizer()
            
            # Test portfolio optimization
            portfolios = optimizer.optimize_portfolios(self.sample_returns)
            
            assert len(portfolios) > 0, "No portfolios optimized"
            
            # Test portfolio structure
            for portfolio in portfolios:
                assert isinstance(portfolio, Portfolio)
                assert len(portfolio.pairs) == 6, f"Portfolio has {len(portfolio.pairs)} pairs, expected 6"
                assert len(portfolio.weights) == len(portfolio.pairs)
                assert portfolio.strategy in ['max_sharpe', 'max_sortino', 'min_variance']
            
            # Test portfolio validation
            for portfolio in portfolios:
                is_valid, message = optimizer.validate_portfolio(portfolio)
                assert is_valid, f"Portfolio validation failed: {message}"
            
            return True
            
        except Exception as e:
            logger.error(f"Portfolio optimizer test failed: {str(e)}")
            return False
    
    def test_portfolio_presenter(self) -> bool:
        """Test portfolio presentation"""
        try:
            presenter = PortfolioPresenter()
            
            # Create test portfolios
            test_pairs = list(self.sample_returns.columns)[:6]
            test_weights = [1.0/6] * 6
            test_portfolio = Portfolio(test_pairs, test_weights, 'test_strategy')
            test_portfolio.add_metrics({
                'sharpe_ratio': 1.5,
                'volatility': 0.01,
                'expected_return': 0.001
            })
            
            # Test presentation
            presentation = presenter.present_portfolios([test_portfolio], self.sample_returns)
            
            assert presentation['num_portfolios'] > 0
            assert 'summary' in presentation
            assert 'portfolios' in presentation
            
            # Test export
            json_export = presenter.export_presentation(presentation, 'json')
            assert len(json_export) > 0
            
            return True
            
        except Exception as e:
            logger.error(f"Portfolio presenter test failed: {str(e)}")
            return False
    
    def test_mt5_connector(self) -> bool:
        """Test MT5 connector (may fail if MT5 not available)"""
        try:
            connector = MT5Connector()
            
            # Test connection (this may fail if MT5 is not available)
            try:
                connected = connector.connect()
                if connected:
                    logger.info("MT5 connection successful")
                    
                    # Test market status
                    status = connector.get_market_status()
                    assert isinstance(status, dict)
                    
                    # Test symbol info
                    symbol_info = connector.get_symbol_info("EURUSD")
                    if symbol_info:
                        assert 'name' in symbol_info
                    
                    connector.disconnect()
                    return True
                else:
                    logger.warning("MT5 connection failed - this is expected if MT5 is not running")
                    return True  # Don't fail the test if MT5 is not available
                    
            except Exception as e:
                logger.warning(f"MT5 test skipped: {str(e)}")
                return True  # Don't fail the test if MT5 is not available
            
        except Exception as e:
            logger.error(f"MT5 connector test failed: {str(e)}")
            return False
    
    def test_integration(self) -> bool:
        """Test integration between modules"""
        try:
            # Test complete workflow with sample data
            returns_calc = LogReturnsCalculator()
            risk_calc = RiskCalculator()
            optimizer = PortfolioOptimizer()
            weight_mgr = WeightManager()
            presenter = PortfolioPresenter()
            
            # Calculate returns
            log_returns = returns_calc.calculate_log_returns(self.sample_data)
            
            # Calculate risk
            risk_profiles = risk_calc.calculate_risk_profiles(log_returns)
            
            # Optimize portfolios
            portfolios = optimizer.optimize_portfolios(log_returns)
            
            # Present portfolios
            presentation = presenter.present_portfolios(portfolios, log_returns)
            
            # Verify integration
            assert not log_returns.empty
            assert len(risk_profiles) > 0
            assert len(portfolios) > 0
            assert presentation['num_portfolios'] > 0
            
            return True
            
        except Exception as e:
            logger.error(f"Integration test failed: {str(e)}")
            return False
    
    def test_application_class(self) -> bool:
        """Test the main application class"""
        try:
            app = MatrixQPApplication()
            
            # Test status
            status = app.get_status()
            assert isinstance(status, dict)
            assert 'is_running' in status
            
            # Test shutdown
            app.shutdown()
            
            return True
            
        except Exception as e:
            logger.error(f"Application class test failed: {str(e)}")
            return False
    
    def print_test_summary(self):
        """Print test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        print("\n" + "="*60)
        print("MATRIX QP TEST SUMMARY")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        print("="*60)
        
        if failed_tests > 0:
            print("\nFAILED TESTS:")
            for test_name, result in self.test_results.items():
                if not result:
                    print(f"  ✗ {test_name}")
        else:
            print("\n🎉 ALL TESTS PASSED!")
        
        print("="*60)


def main():
    """Main test function"""
    print("Matrix QP - Comprehensive Test Suite")
    print("====================================")
    
    tester = MatrixQPTester()
    results = tester.run_all_tests()
    
    # Return appropriate exit code
    if all(results.values()):
        sys.exit(0)  # Success
    else:
        sys.exit(1)  # Failure


if __name__ == "__main__":
    main()
