"""
Matrix QP - Main Application Entry Point
Quantitative Portfolio Optimization for Forex Trading

This is the main application that integrates all modules and provides
the complete portfolio optimization solution.
"""

import sys
import os
import logging
import argparse
import asyncio
from datetime import datetime
from typing import Optional, Dict, List
import warnings

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import all modules
from config import (
    CURRENCY_PAIRS, DASH_HOST, DASH_PORT, LOG_LEVEL, LOG_FORMAT, LOG_FILE,
    get_market_start_time
)
from mt5_connector import MT5Connector
from log_returns import LogReturnsCalculator
from risk_calculator import RiskCalculator
from portfolio_optimizer import PortfolioOptimizer
from weight_manager import WeightManager
from portfolio_presenter import PortfolioPresenter
from dashboard import PortfolioDashboard

# Suppress warnings
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class MatrixQPApplication:
    """
    Main application class for Matrix QP Portfolio Optimization
    Orchestrates all components and provides the complete solution
    """
    
    def __init__(self):
        """Initialize the Matrix QP application"""
        logger.info("Initializing Matrix QP Application")
        
        # Initialize all components
        self.mt5_connector = MT5Connector()
        self.returns_calculator = LogReturnsCalculator()
        self.risk_calculator = RiskCalculator()
        self.portfolio_optimizer = PortfolioOptimizer()
        self.weight_manager = WeightManager()
        self.portfolio_presenter = PortfolioPresenter()
        self.dashboard = PortfolioDashboard()
        
        # Application state
        self.current_data = {}
        self.current_returns = None
        self.current_portfolios = []
        self.current_presentation = {}
        self.is_running = False
        
        logger.info("Matrix QP Application initialized successfully")
    
    async def initialize(self) -> bool:
        """
        Initialize the application and establish connections
        
        Returns:
            bool: True if initialization successful
        """
        logger.info("Starting application initialization...")
        
        try:
            # Connect to MT5
            if not self.mt5_connector.connect():
                logger.error("Failed to connect to MetaTrader 5")
                return False
            
            logger.info("MT5 connection established")

            # Verify market data availability
            market_status = self.mt5_connector.get_market_status()
            available_pairs = [pair for pair, available in market_status.items() if available]

            if len(available_pairs) < 10:  # Need at least 10 pairs for meaningful optimization
                logger.warning(f"Only {len(available_pairs)} pairs available, may affect optimization quality")
            else:
                logger.info(f"{len(available_pairs)} currency pairs available for optimization")

            # Test data fetching
            test_data = self.mt5_connector.fetch_daily_data(CURRENCY_PAIRS[:3])
            if not test_data:
                logger.error("Failed to fetch test data from MT5")
                return False

            logger.info("Data fetching capability verified")
            
            self.is_running = True
            logger.info("Application initialization complete")
            return True
            
        except Exception as e:
            logger.error(f"Application initialization failed: {str(e)}")
            return False
    
    async def run_optimization_cycle(self, total_lot_size: float = 1.0) -> Dict:
        """
        Run a complete optimization cycle
        
        Args:
            total_lot_size: Total lot size for position sizing
            
        Returns:
            Dictionary with optimization results
        """
        logger.info("Starting optimization cycle...")
        
        try:
            # Step 1: Fetch current market data
            logger.info("Fetching market data...")
            market_data = self.mt5_connector.fetch_daily_data()
            
            if not market_data:
                raise Exception("No market data available")
            
            self.current_data = market_data
            logger.info(f"✓ Fetched data for {len(market_data)} pairs")
            
            # Step 2: Calculate log returns
            logger.info("Calculating log returns...")
            log_returns = self.returns_calculator.calculate_log_returns(market_data)
            
            if log_returns.empty:
                raise Exception("Failed to calculate log returns")
            
            self.current_returns = log_returns
            logger.info(f"✓ Calculated returns for {len(log_returns.columns)} pairs")
            
            # Step 3: Calculate risk profiles
            logger.info("Calculating risk profiles...")
            risk_profiles = self.risk_calculator.calculate_risk_profiles(log_returns)
            
            if not risk_profiles:
                raise Exception("Failed to calculate risk profiles")
            
            logger.info(f"✓ Calculated risk profiles for {len(risk_profiles)} pairs")
            
            # Step 4: Optimize portfolios
            logger.info("Optimizing portfolios...")
            portfolios = self.portfolio_optimizer.optimize_portfolios(log_returns)
            
            if not portfolios:
                raise Exception("Portfolio optimization failed")
            
            self.current_portfolios = portfolios
            logger.info(f"✓ Optimized {len(portfolios)} portfolios")
            
            # Step 5: Present portfolios
            logger.info("Preparing portfolio presentation...")
            presentation = self.portfolio_presenter.present_portfolios(
                portfolios, log_returns, total_lot_size
            )
            
            self.current_presentation = presentation
            logger.info(f"✓ Prepared presentation for {presentation['num_portfolios']} portfolios")
            
            # Step 6: Update dashboard data
            self._update_dashboard_data()
            
            # Create result summary
            result = {
                'timestamp': datetime.now().isoformat(),
                'status': 'success',
                'data_points': len(market_data),
                'return_pairs': len(log_returns.columns),
                'risk_profiles': len(risk_profiles),
                'optimized_portfolios': len(portfolios),
                'presentation': presentation,
                'summary': {
                    'best_sharpe': max(p.metrics.get('sharpe_ratio', 0) for p in portfolios),
                    'min_volatility': min(p.metrics.get('volatility', float('inf')) for p in portfolios),
                    'avg_return': sum(p.metrics.get('expected_return', 0) for p in portfolios) / len(portfolios)
                }
            }
            
            logger.info("Optimization cycle completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Optimization cycle failed: {str(e)}")
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e),
                'data_points': 0,
                'return_pairs': 0,
                'risk_profiles': 0,
                'optimized_portfolios': 0
            }
    
    def _update_dashboard_data(self):
        """Update dashboard with current data"""
        try:
            # Convert portfolios to dashboard format
            dashboard_portfolios = []
            
            for portfolio in self.current_portfolios:
                dashboard_portfolio = {
                    'strategy': portfolio.strategy,
                    'pairs': portfolio.pairs,
                    'weights': portfolio.weights,
                    'metrics': portfolio.metrics
                }
                dashboard_portfolios.append(dashboard_portfolio)
            
            # Update dashboard data stores
            self.dashboard.current_portfolios = dashboard_portfolios
            self.dashboard.current_returns = self.current_returns
            
            logger.debug("Dashboard data updated successfully")
            
        except Exception as e:
            logger.error(f"Failed to update dashboard data: {str(e)}")
    
    def run_dashboard(self, host: str = DASH_HOST, port: int = DASH_PORT):
        """
        Run the web dashboard
        
        Args:
            host: Dashboard host address
            port: Dashboard port
        """
        logger.info(f"Starting Matrix QP Dashboard on {host}:{port}")
        
        try:
            # Integrate optimization functionality with dashboard
            self._setup_dashboard_integration()
            
            # Run the dashboard
            self.dashboard.run(host=host, port=port)
            
        except Exception as e:
            logger.error(f"Dashboard failed to start: {str(e)}")
            raise
    
    def _setup_dashboard_integration(self):
        """Setup integration between optimization engine and dashboard"""
        # Add optimization callback to dashboard
        original_update_portfolios = None
        
        # Find the update_portfolios callback
        for callback in self.dashboard.app.callback_map.values():
            if hasattr(callback, 'function') and 'update_portfolios' in str(callback.function):
                original_update_portfolios = callback.function
                break
        
        if original_update_portfolios:
            # Wrap the callback to use real optimization
            async def enhanced_update_portfolios(*args, **kwargs):
                try:
                    # Run optimization cycle
                    result = await self.run_optimization_cycle()
                    
                    if result['status'] == 'success':
                        return (
                            self.dashboard.current_portfolios,
                            {'status': 'complete', 'timestamp': result['timestamp']},
                            f"✓ Optimized {result['optimized_portfolios']} portfolios"
                        )
                    else:
                        return (
                            [],
                            {'status': 'error', 'error': result.get('error', 'Unknown error')},
                            f"✗ Error: {result.get('error', 'Optimization failed')}"
                        )
                        
                except Exception as e:
                    logger.error(f"Enhanced update portfolios failed: {str(e)}")
                    return ([], {'status': 'error', 'error': str(e)}, f"✗ Error: {str(e)}")
            
            # Note: In a real implementation, you would properly replace the callback
            logger.info("Dashboard integration setup complete")
    
    def get_status(self) -> Dict:
        """Get current application status"""
        return {
            'is_running': self.is_running,
            'mt5_connected': self.mt5_connector.is_connected(),
            'last_optimization': self.current_presentation.get('timestamp'),
            'available_portfolios': len(self.current_portfolios),
            'data_pairs': len(self.current_data),
            'return_pairs': len(self.current_returns.columns) if self.current_returns is not None else 0
        }
    
    def shutdown(self):
        """Shutdown the application gracefully"""
        logger.info("Shutting down Matrix QP Application...")
        
        try:
            # Disconnect from MT5
            self.mt5_connector.disconnect()
            
            # Clear data
            self.current_data.clear()
            self.current_portfolios.clear()
            self.current_presentation.clear()
            
            self.is_running = False
            logger.info("Application shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")


def main():
    """Main entry point for the application"""
    parser = argparse.ArgumentParser(description='Matrix QP - Quantitative Portfolio Optimization')
    parser.add_argument('--mode', choices=['dashboard', 'optimize', 'status'], 
                       default='dashboard', help='Application mode')
    parser.add_argument('--host', default=DASH_HOST, help='Dashboard host')
    parser.add_argument('--port', type=int, default=DASH_PORT, help='Dashboard port')
    parser.add_argument('--lot-size', type=float, default=1.0, help='Total lot size for optimization')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default=LOG_LEVEL, help='Logging level')
    
    args = parser.parse_args()
    
    # Update logging level if specified
    if args.log_level != LOG_LEVEL:
        logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Create application instance
    app = MatrixQPApplication()
    
    try:
        if args.mode == 'dashboard':
            # Initialize and run dashboard
            if asyncio.run(app.initialize()):
                logger.info("Starting Matrix QP Dashboard...")
                app.run_dashboard(host=args.host, port=args.port)
            else:
                logger.error("Failed to initialize application")
                sys.exit(1)
                
        elif args.mode == 'optimize':
            # Run single optimization cycle
            if asyncio.run(app.initialize()):
                logger.info("Running optimization cycle...")
                result = asyncio.run(app.run_optimization_cycle(args.lot_size))
                
                if result['status'] == 'success':
                    print(f"\nOptimization successful!")
                    print(f"  Portfolios: {result['optimized_portfolios']}")
                    print(f"  Best Sharpe: {result['summary']['best_sharpe']:.3f}")
                    print(f"  Min Volatility: {result['summary']['min_volatility']:.4f}")
                    print(f"  Avg Return: {result['summary']['avg_return']:.6f}")
                else:
                    print(f"\nOptimization failed: {result.get('error', 'Unknown error')}")
                    sys.exit(1)
            else:
                logger.error("Failed to initialize application")
                sys.exit(1)
                
        elif args.mode == 'status':
            # Show application status
            if asyncio.run(app.initialize()):
                status = app.get_status()
                print(f"\nMatrix QP Status:")
                print(f"  Running: {status['is_running']}")
                print(f"  MT5 Connected: {status['mt5_connected']}")
                print(f"  Available Portfolios: {status['available_portfolios']}")
                print(f"  Data Pairs: {status['data_pairs']}")
                print(f"  Return Pairs: {status['return_pairs']}")
                if status['last_optimization']:
                    print(f"  Last Optimization: {status['last_optimization']}")
            else:
                print("Application not initialized")
                sys.exit(1)
    
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {str(e)}")
        sys.exit(1)
    finally:
        app.shutdown()


if __name__ == "__main__":
    main()
