"""
Test script for the alert system
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import logging

# Configure logging to see debug messages
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from alert_system import TakeoffDetector, AlertManager, check_for_takeoff, send_alert

def create_test_data():
    """Create test normalized returns data that should trigger an alert"""
    
    # Create timestamps for the last hour
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    timestamps = pd.date_range(start=start_time, end=end_time, freq='1min')
    
    # Currency pairs
    pairs = ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDCHF', 'USDJPY']
    
    # Create DataFrame
    df = pd.DataFrame(index=timestamps, columns=pairs)
    
    # Fill with initial small random values
    for pair in pairs:
        df[pair] = np.random.normal(0, 0.1, len(timestamps))
    
    # Simulate takeoff pattern in the last 5 minutes for 6 pairs (should trigger alert)
    takeoff_start = len(timestamps) - 5
    takeoff_pairs = pairs[:6]  # First 6 pairs
    
    for i, pair in enumerate(takeoff_pairs):
        # Create accelerating pattern
        for j in range(takeoff_start, len(timestamps)):
            acceleration = (j - takeoff_start) ** 2 * 0.1  # Quadratic acceleration
            df.iloc[j, df.columns.get_loc(pair)] = 0.5 + acceleration + np.random.normal(0, 0.05)
    
    return df

def test_alert_system():
    """Test the alert system with simulated data"""
    print("Testing Matrix QP Alert System")
    print("=" * 40)
    
    # Create test data
    print("Creating test data with takeoff pattern...")
    test_data = create_test_data()
    
    print(f"Test data shape: {test_data.shape}")
    print(f"Latest values:")
    print(test_data.tail(3))
    
    # Test takeoff detection
    print("\nTesting takeoff detection...")
    detector = TakeoffDetector(
        min_pairs_threshold=5,
        acceleration_threshold=0.1,  # Much lower threshold for testing
        magnitude_threshold=0.1,     # Much lower threshold for testing
        lookback_periods=3
    )

    # Debug: Check the acceleration calculation manually
    print("\nDebugging acceleration calculation:")
    latest_data = test_data.tail(3)
    for pair in test_data.columns[:6]:  # Check first 6 pairs
        series = latest_data[pair].dropna()
        if len(series) >= 3:
            first_diff = series.diff()
            second_diff = first_diff.diff()
            print(f"{pair}: values={series.values}, 1st_diff={first_diff.values}, 2nd_diff={second_diff.values}")
            print(f"  Latest acceleration: {second_diff.iloc[-1]:.3f}, magnitude: {abs(series.iloc[-1]):.3f}")
    print()
    
    alert_event = detector.detect_takeoff(test_data)
    
    if alert_event:
        print("✅ ALERT DETECTED!")
        print(f"Alert Type: {alert_event.alert_type}")
        print(f"Timestamp: {alert_event.timestamp}")
        print(f"Pairs Involved: {alert_event.pairs_involved}")
        print(f"Magnitude: {alert_event.magnitude:.3f}")
        print(f"Message: {alert_event.message}")
        
        # Test alert manager
        print("\nTesting alert delivery...")
        alert_manager = AlertManager()
        alert_manager.send_alert(alert_event)
        
        print("✅ Alert sent successfully!")
        
        # Show recent alerts
        recent_alerts = alert_manager.get_recent_alerts(5)
        print(f"\nRecent alerts count: {len(recent_alerts)}")
        
    else:
        print("❌ No alert detected")
        print("This might be due to:")
        print("- Thresholds too high")
        print("- Not enough pairs accelerating")
        print("- Insufficient data points")
    
    print("\n" + "=" * 40)
    print("Test completed!")

if __name__ == "__main__":
    test_alert_system()
