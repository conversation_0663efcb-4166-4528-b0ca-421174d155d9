# Dispersion Breakout Portfolio Strategy (SPECS.md)

---

## 1  Objective

Trade intraday or day‑long trends in major‑FX baskets by exploiting **dispersion breakouts**— sudden expansions in cross‑sectional volatility that often precede directional moves.

- Enter **within ≤ 30 minutes** of a qualified dispersion spike (red‑box events).
- Hold positions for **several hours** (same‑day) or until the trend/volatility regime ends.
- Allocate via pre‑computed MPT portfolios (<PERSON>, Sortino, Calmar, Omega, Min V<PERSON> …).

---

## 2  Signal Detection

| Step | Rule                    | Default Parameter                    |
| ---- | ----------------------- | ------------------------------------ |
| 2.1  | **CSSD threshold ≥ T₁** | 95‑th pct. of last N mins (N = 240)  |
| 2.2  | **ROC filter ≥ T₂**     | ΔCSSD ≥ 2 σ within 15 mins           |
| 2.3  | **Persistence**         | CSSD stays > T₁ for ≥ 3 bars         |
| 2.4  | **Pre‑quiet check**     | Previous 60 mins CSSD < 50‑th pct.   |
| 2.5  | **Trend confirm**       | ADX(14) > 25 **and rising**          |
| 2.6  | **News filter**         | Skip if major scheduled news ±2 mins |

---

## 3  Directional Alignment

1. **Rank normalized returns** of all 28 pairs at trigger time.
2. Identify dominant currency ‑‑ at least **3 pairs** with that currency in the same direction & |norm\_ret| > 1 σ.
3. Require cluster correlation > 0.6 (optional).
4. Build a **directional basket** (e.g. long USD vs majors) consistent with step 2.

---

## 4  Portfolio Selection & Rebalance

| Condition                     | Portfolio Choice               | Rationale                           |
| ----------------------------- | ------------------------------ | ----------------------------------- |
| Valid breakout & clear leader | **Max Sharpe / Max Sortino**   | overweight momentum assets          |
| Choppy breakout but wide vol  | **Omega**                      | skew toward higher upside potential |
| Vol spike but mixed direction | **Min Var** (skip trade often) | defensive—usually no entry          |

- Execute weight vector **within 30 min** of trigger.
- Use market orders or staggered limits; priority = full allocation before move matures.

---

## 5  Execution & Risk

- **Position sizing:** \( lot_i = W_i · E / (ATR_i · K)\), where *E* = equity, *K* risk scaling.
- **Initial Stop:** just inside prior range or *Stop\_i = Entry\_i ± 1.2 × ATR\_i* (direction‑specific).
- **Max portfolio VaR:** ≤ 2 % account equity.

---

## 6  Exit Logic

| Exit Trigger                                   | Action                |
| ---------------------------------------------- | --------------------- |
| CSSD contracts below 0.5 × peak **or** < T₁    | Close all / scale‑out |
| Opposite dispersion spike with opposite leader | Flip / exit           |
| Time stop (end‑of‑session)                     | Close residual        |
| Trailing stop hit (Swing Low/High)             | Close affected leg    |

---

## 7  Automation Checklist (MT5)

1. Real‑time feed → compute minute CSSD, ROC, ADX.
2. Evaluate rules 2.1‑2.6 every minute.
3. On signal:
   - Determine dominant currency basket.
   - Load latest portfolio set; select per §4.
   - Rebalance via MT5 bulk order API (weights & lot size).
4. Monitor exit conditions every minute; enforce rule‑based exits.

---

## 8  Parameter Tuning

- **T₁ / CSSD percentile:** back‑test 90‑97 % range.
- **ROC σ‑multiplier:** test 1.5‑3 σ.
- **ADX window:** 14 or 21.

Store tunables in a YAML/JSON config for nightly optimisation.

---

## 9  Back‑Testing Notes

- Use at least **2 years** of minute‑data, include weekends removal.
- Walk‑forward optimise thresholds quarterly; lock portfolios daily.
- Evaluate KPIs: CAGR, Sharpe, max DD, win %, average trade R.

---

## 10  Future Enhancements

- Machine‑learned CSSD regime classifier.
- Vol‑adjusted dynamic portfolio re‑optimisation every 2 h.
- Position‑netting to reduce margin usage.

