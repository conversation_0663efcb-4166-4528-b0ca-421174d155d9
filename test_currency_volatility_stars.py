#!/usr/bin/env python3
"""
Test script for volatility signal stars on Currency CSSD chart
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from dispersion_charts import DispersionChartCreator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data_with_currency_volatility():
    """
    Create test data with clear currency-specific volatility signals
    """
    # Create 8 hours of minute data
    start_time = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    time_index = pd.date_range(start=start_time, periods=480, freq='1min')
    
    # Major currency pairs
    pairs = [
        'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDCHF', 'USDJPY',
        'EURGBP', 'EURAUD', 'EURNZD', 'EURCAD', 'EURCHF', 'EURJPY',
        'GBPAUD', 'GBPNZD', 'GBPCAD', 'GBPCHF', 'GBPJPY',
        'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY',
        'NZDCAD', 'NZDCHF', 'NZDJPY',
        'CADCHF', 'CADJPY',
        'CHFJPY'
    ]
    
    # Create base random data (low volatility baseline)
    np.random.seed(42)
    base_data = np.random.normal(0, 0.0002, (len(time_index), len(pairs)))  # Low volatility baseline
    
    # Add specific currency volatility signals
    signal_times = [120, 240, 360]  # At 2 hours, 4 hours, 6 hours

    for signal_time in signal_times:
        if signal_time < len(time_index):
            # Create coordinated USD movement (multiple USD pairs moving together)
            usd_pairs = [p for p in pairs if 'USD' in p]

            # Create high volatility spike that persists for 5+ bars
            for i in range(8):  # 8 bars of persistence (more than required 3)
                if signal_time + i < len(time_index):
                    # Make USD pairs move in unison with high correlation
                    # USD strengthening scenario: EURUSD down, USDCAD up, USDJPY up
                    usd_strength = 0.003  # Strong USD movement

                    for pair in usd_pairs:
                        pair_idx = pairs.index(pair)
                        # Apply directional consistency based on USD position
                        if pair.startswith('USD'):
                            # USD is base: USD strength = pair goes up
                            base_data[signal_time + i, pair_idx] = usd_strength
                        else:
                            # USD is quote: USD strength = pair goes down
                            base_data[signal_time + i, pair_idx] = -usd_strength
                            
            # Also create EUR volatility signals
            eur_pairs = [p for p in pairs if 'EUR' in p]
            for i in range(6):  # 6 bars of persistence
                if signal_time + 60 + i < len(time_index):  # Offset by 1 hour
                    # EUR strengthening scenario: EURUSD up, EURGBP up, EURJPY up
                    eur_strength = 0.002

                    for pair in eur_pairs:
                        pair_idx = pairs.index(pair)
                        if pair.startswith('EUR'):
                            # EUR is base: EUR strength = pair goes up
                            base_data[signal_time + 60 + i, pair_idx] = eur_strength
                        else:
                            # EUR is quote: EUR strength = pair goes down
                            base_data[signal_time + 60 + i, pair_idx] = -eur_strength
    
    # Convert to normalized returns (not cumulative for currency chart)
    normalized_data = base_data
    
    # Create DataFrame
    df = pd.DataFrame(normalized_data, index=time_index, columns=pairs)
    
    logger.info(f"Created test data with {len(pairs)} pairs and {len(time_index)} time points")
    logger.info(f"Added volatility signals at times: {signal_times}")
    
    return df

def main():
    logger.info("Starting Currency Volatility Signal Stars Test...")
    
    # Create test data with clear volatility signals
    test_data = create_test_data_with_currency_volatility()
    
    # Initialize dispersion charts
    charts = DispersionChartCreator()
    
    # Create currency CSSD chart
    logger.info("Creating currency CSSD chart with volatility signal stars...")
    
    # Create the chart
    fig, pair_contributions = charts.create_currency_cssd_chart(test_data)
    
    # Count annotations (stars)
    annotation_count = len(fig.layout.annotations) if hasattr(fig.layout, 'annotations') and fig.layout.annotations else 0
    
    logger.info(f"Chart created with {len(fig.data)} traces")
    logger.info(f"Chart created with {annotation_count} star annotations")
    
    # Save chart for inspection
    try:
        fig.write_html("currency_volatility_stars_test.html")
        logger.info("Chart saved as 'currency_volatility_stars_test.html'")
    except Exception as e:
        logger.warning(f"Could not save chart: {e}")
    
    logger.info("✅ Currency volatility signal stars test completed!")
    
    if annotation_count > 0:
        logger.info(f"✅ Currency volatility signal stars are working! Found {annotation_count} star annotations.")
    else:
        logger.warning("⚠️ No star annotations found. May need to adjust signal detection sensitivity.")

if __name__ == "__main__":
    main()
