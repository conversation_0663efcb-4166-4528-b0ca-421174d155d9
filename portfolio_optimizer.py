"""
Portfolio Optimization Engine for Matrix QP
Implements multi-objective optimization with currency distribution constraints
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from itertools import combinations
from scipy.optimize import minimize
from concurrent.futures import ProcessPoolExecutor, as_completed
import warnings
from joblib import Parallel, delayed
import multiprocessing as mp
import psutil

import config
from config import (
    PORTFOLIO_SIZE, MIN_PORTFOLIOS, MAX_CURRENCY_OCCURRENCE, MAX_CURRENCY_TOTAL_OCCURRENCE,
    OPTIMIZATION_STRATEGIES, MIN_WEIGHT, MAX_WEIGHT,
    OPTIMIZATION_METHOD, MAX_ITERATIONS, OPTIMIZATION_TOLERANCE,
    MAX_WORKERS, validate_portfolio_distribution, get_currency_from_pair,
    USE_CVAR_FOR_VOLATILITY, CVAR_CONFIDENCE_LEVEL,
    ENABLE_TWO_STAGE_OPTIMIZATION, STAGE1_MAX_ITERATIONS, STAGE1_TOLERANCE,
    STAGE1_TOP_COMBINATIONS_RATIO, STAGE2_MAX_ITERATIONS, STAGE2_TOLERANCE,
    MAX_PAIR_APPEARANCES, ENABLE_PAIR_APPEARANCE_LIMIT, ENSURE_STRATEGY_REPRESENTATION,
    USE_JOBLIB_MULTIPROCESSING, JOBLIB_BACKEND, JOBLIB_VERBOSE,
    JOBLIB_MAX_NBYTES, JOBLIB_BATCH_SIZE, CURRENCIES
)
from risk_calculator import RiskCalculator, calculate_var_cvar_numba

# Configure logging
logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore', category=RuntimeWarning)


def get_adaptive_n_jobs():
    """Dynamically determine optimal number of cores based on current CPU load"""
    try:
        current_cpu = psutil.cpu_percent(interval=0.1)
        total_cores = mp.cpu_count()

        # Adaptive core allocation based on CPU load
        if current_cpu > 80:
            return max(2, total_cores // 4)  # Use 25% of cores when CPU is very high
        elif current_cpu > 60:
            return max(4, total_cores // 2)  # Use 50% of cores when CPU is moderately high
        else:
            return max(4, int(total_cores * 0.75))  # Use 75% of cores when CPU is normal
    except Exception as e:
        logger.warning(f"Error getting CPU info: {e}, defaulting to {MAX_WORKERS} workers")
        return MAX_WORKERS


def get_joblib_parallel():
    """Create and configure joblib Parallel instance"""
    if USE_JOBLIB_MULTIPROCESSING:
        return Parallel(
            n_jobs=get_adaptive_n_jobs(),
            verbose=JOBLIB_VERBOSE,
            prefer="processes",
            backend=JOBLIB_BACKEND,
            batch_size=JOBLIB_BATCH_SIZE,
            max_nbytes=JOBLIB_MAX_NBYTES
        )
    else:
        return None


# Global joblib parallel executor
_parallel_executor = None


def get_parallel_executor():
    """Get or create the global parallel executor"""
    global _parallel_executor
    if _parallel_executor is None and USE_JOBLIB_MULTIPROCESSING:
        _parallel_executor = get_joblib_parallel()
    return _parallel_executor


# Standalone optimization function for joblib multiprocessing
def optimize_combination_standalone(log_returns_data, combination, strategy, max_iter=None, tolerance=None):
    """
    Standalone function for optimizing a single combination - designed for joblib multiprocessing

    Args:
        log_returns_data: Dictionary with returns data (to avoid pickling issues)
        combination: Tuple of currency pairs
        strategy: Optimization strategy
        max_iter: Maximum iterations
        tolerance: Optimization tolerance

    Returns:
        Dictionary with portfolio data or None if failed
    """
    try:
        # Reconstruct DataFrame from data
        log_returns = pd.DataFrame(log_returns_data['data'],
                                 index=pd.to_datetime(log_returns_data['index'], format='%Y-%m-%d %H:%M:%S'),
                                 columns=log_returns_data['columns'])

        # Extract returns for the combination
        combo_returns = log_returns[list(combination)].dropna()

        if len(combo_returns) < 30:  # Need sufficient data
            return None

        # Use provided parameters or defaults
        max_iterations = max_iter if max_iter is not None else MAX_ITERATIONS
        opt_tolerance = tolerance if tolerance is not None else OPTIMIZATION_TOLERANCE

        # Calculate statistics
        mean_returns = combo_returns.mean().values
        cov_matrix = combo_returns.cov().values

        # Set up optimization
        n_assets = len(combination)
        initial_weights = np.array([1.0 / n_assets] * n_assets)

        # Bounds for weights
        bounds = tuple((MIN_WEIGHT, MAX_WEIGHT) for _ in range(n_assets))

        # Constraint: weights sum to 1
        constraints = [{'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}]

        # Choose objective function based on strategy
        if strategy == 'max_sharpe':
            objective = lambda w: -_calculate_sharpe_ratio_standalone(w, mean_returns, cov_matrix, combo_returns)
        elif strategy == 'max_sortino':
            objective = lambda w: -_calculate_sortino_ratio_standalone(w, combo_returns.values)
        elif strategy == 'max_omega':
            objective = lambda w: -_calculate_omega_ratio_standalone(w, combo_returns.values)
        elif strategy == 'max_calmar':
            objective = lambda w: -_calculate_calmar_ratio_standalone(w, combo_returns.values)
        elif strategy == 'min_variance':
            if USE_CVAR_FOR_VOLATILITY:
                objective = lambda w: calculate_var_cvar_numba(combo_returns.dot(w).values, CVAR_CONFIDENCE_LEVEL)[1]
            else:
                objective = lambda w: _calculate_portfolio_variance_standalone(w, cov_matrix)
        else:
            return None

        # Optimize
        result = minimize(
            objective,
            initial_weights,
            method=OPTIMIZATION_METHOD,
            bounds=bounds,
            constraints=constraints,
            options={'maxiter': max_iterations, 'ftol': opt_tolerance}
        )

        if not result.success:
            return None

        # Create portfolio data dictionary
        weights = result.x / np.sum(result.x)  # Normalize weights to sum to 1

        # Calculate metrics
        metrics = _calculate_portfolio_metrics_standalone(weights, mean_returns, cov_matrix, combo_returns)

        return {
            'pairs': list(combination),
            'weights': weights.tolist(),
            'strategy': strategy,
            'metrics': metrics
        }

    except Exception as e:
        logger.debug(f"Error optimizing combination {combination}: {str(e)}")
        return None


# Standalone helper functions for multiprocessing
def _calculate_sharpe_ratio_standalone(weights, mean_returns, cov_matrix, returns_df):
    """Standalone Sharpe ratio calculation"""
    portfolio_return = np.sum(mean_returns * weights)

    if USE_CVAR_FOR_VOLATILITY:
        portfolio_returns = returns_df.dot(weights)
        _, portfolio_cvar = calculate_var_cvar_numba(portfolio_returns.values, CVAR_CONFIDENCE_LEVEL)
        portfolio_risk = portfolio_cvar
    else:
        portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
        portfolio_risk = np.sqrt(portfolio_variance)

    if portfolio_risk == 0:
        return 0.0
    return portfolio_return / portfolio_risk


def _calculate_sortino_ratio_standalone(weights, returns_array):
    """Standalone Sortino ratio calculation"""
    portfolio_returns = returns_array.dot(weights)
    mean_return = np.mean(portfolio_returns)
    downside_returns = portfolio_returns[portfolio_returns < 0]

    if len(downside_returns) == 0:
        return float('inf')

    downside_deviation = np.sqrt(np.mean(downside_returns**2))
    if downside_deviation == 0:
        return 0.0

    return mean_return / downside_deviation


def _calculate_omega_ratio_standalone(weights, returns_array, threshold=0.0):
    """Standalone Omega ratio calculation"""
    portfolio_returns = returns_array.dot(weights)
    excess_returns = portfolio_returns - threshold

    gains = excess_returns[excess_returns > 0]
    losses = excess_returns[excess_returns < 0]

    if len(gains) == 0:
        return 0.0
    if len(losses) == 0:
        return float('inf')

    return np.sum(gains) / abs(np.sum(losses))


def _calculate_calmar_ratio_standalone(weights, returns_array):
    """Standalone Calmar ratio calculation"""
    portfolio_returns = returns_array.dot(weights)
    cumulative_returns = np.cumprod(1 + portfolio_returns)

    # Calculate maximum drawdown
    peak = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - peak) / peak
    max_drawdown = abs(np.min(drawdown))

    if max_drawdown == 0:
        return float('inf')

    annual_return = np.mean(portfolio_returns) * 252  # Annualized
    return annual_return / max_drawdown


def _calculate_portfolio_variance_standalone(weights, cov_matrix):
    """Standalone portfolio variance calculation"""
    return np.dot(weights.T, np.dot(cov_matrix, weights))


def _calculate_portfolio_metrics_standalone(weights, mean_returns, cov_matrix, combo_returns):
    """Standalone portfolio metrics calculation"""
    portfolio_return = np.sum(mean_returns * weights)
    portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
    portfolio_std = np.sqrt(portfolio_variance)

    # Calculate portfolio returns series
    portfolio_returns = combo_returns.dot(weights)

    # Calculate CVaR if enabled
    if USE_CVAR_FOR_VOLATILITY:
        _, portfolio_cvar = calculate_var_cvar_numba(portfolio_returns.values, CVAR_CONFIDENCE_LEVEL)
        portfolio_volatility = portfolio_cvar
    else:
        portfolio_volatility = portfolio_std

    # Calculate ratios
    sharpe_ratio = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0.0
    sortino_ratio = _calculate_sortino_ratio_standalone(weights, combo_returns.values)
    omega_ratio = _calculate_omega_ratio_standalone(weights, combo_returns.values)
    calmar_ratio = _calculate_calmar_ratio_standalone(weights, combo_returns.values)

    # Calculate max drawdown
    cumulative_returns = np.cumprod(1 + portfolio_returns)
    peak = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - peak) / peak
    max_drawdown = abs(np.min(drawdown))

    return {
        'expected_return': float(portfolio_return),
        'volatility': float(portfolio_volatility),
        'variance': float(portfolio_variance),
        'sharpe_ratio': float(sharpe_ratio),
        'sortino_ratio': float(sortino_ratio),
        'omega_ratio': float(omega_ratio),
        'calmar_ratio': float(calmar_ratio),
        'max_drawdown': float(max_drawdown),
        'std_volatility': float(portfolio_std),
        'cvar_volatility': float(portfolio_cvar) if USE_CVAR_FOR_VOLATILITY else None
    }


class Portfolio:
    """Portfolio data structure"""
    
    def __init__(self, pairs: List[str], weights: List[float], strategy: str):
        self.pairs = pairs
        self.weights = weights
        self.strategy = strategy
        self.metrics = {}
        self.risk_profile = {}
    
    def add_metrics(self, metrics: Dict[str, float]):
        """Add performance metrics to the portfolio"""
        self.metrics.update(metrics)
    
    def add_risk_profile(self, risk_profile: Dict[str, float]):
        """Add risk profile to the portfolio"""
        self.risk_profile.update(risk_profile)
    
    def get_weight_dict(self) -> Dict[str, float]:
        """Get weights as a dictionary"""
        return dict(zip(self.pairs, self.weights))
    
    def __repr__(self):
        return f"Portfolio({self.strategy}, {len(self.pairs)} pairs, Sharpe: {self.metrics.get('sharpe_ratio', 'N/A'):.3f})"


class PortfolioOptimizer:
    """
    Advanced portfolio optimizer with multi-objective optimization
    Handles currency distribution constraints and multiple optimization strategies
    """
    
    def __init__(self):
        """Initialize the portfolio optimizer"""
        self.risk_calculator = RiskCalculator()
        self.cached_combinations = None
        self.optimization_results = []
    
    def optimize_portfolios(self,
                          log_returns: pd.DataFrame,
                          strategies: Optional[List[str]] = None,
                          max_portfolios_per_strategy: int = 5,
                          enable_diversification: bool = None) -> List[Portfolio]:
        """
        Optimize portfolios using multiple strategies

        Args:
            log_returns: DataFrame with log returns for each pair
            strategies: List of optimization strategies (default: all configured)
            max_portfolios_per_strategy: Maximum portfolios per strategy
            enable_diversification: Enable diversification across strategies (default: from config)

        Returns:
            List of optimized Portfolio objects
        """
        if strategies is None:
            strategies = OPTIMIZATION_STRATEGIES

        if enable_diversification is None:
            enable_diversification = config.ENABLE_PORTFOLIO_DIVERSIFICATION

        logger.info(f"Starting portfolio optimization with {len(strategies)} strategies")
        if enable_diversification:
            logger.info(f"Portfolio diversification enabled: max reuse weight {config.MAX_PAIR_REUSE_WEIGHT:.1%}")
        
        if log_returns.empty:
            logger.warning("Empty log returns DataFrame provided")
            return []
        
        # Generate valid combinations
        valid_combinations = self._generate_valid_combinations(log_returns.columns.tolist())
        logger.info(f"Generated {len(valid_combinations)} valid combinations")
        
        if not valid_combinations:
            logger.error("No valid combinations found")
            return []
        
        # Optimize for each strategy
        all_portfolios = []
        used_pairs_weights = {}  # Track pairs and their weights across strategies

        for strategy in strategies:
            logger.info(f"Optimizing for strategy: {strategy}")

            try:
                # Apply diversification constraints if enabled
                filtered_combinations = valid_combinations
                if enable_diversification and used_pairs_weights:
                    filtered_combinations = self._filter_combinations_for_diversification(
                        valid_combinations, used_pairs_weights
                    )
                    logger.info(f"Diversification filtering: {len(filtered_combinations)} combinations available")

                strategy_portfolios = self._optimize_strategy(
                    log_returns, filtered_combinations, strategy, max_portfolios_per_strategy
                )
                all_portfolios.extend(strategy_portfolios)
                logger.info(f"Found {len(strategy_portfolios)} portfolios for {strategy}")

                # Track significant pairs from this strategy for diversification
                if enable_diversification and strategy_portfolios:
                    self._update_used_pairs_weights(strategy_portfolios[0], used_pairs_weights)

            except Exception as e:
                logger.error(f"Error optimizing strategy {strategy}: {str(e)}")
        
        # Sort portfolios by performance and select best ones
        best_portfolios = self._select_best_portfolios(all_portfolios)
        
        logger.info(f"Optimization complete. Selected {len(best_portfolios)} best portfolios")
        return best_portfolios
    
    def _generate_valid_combinations(self, available_pairs: List[str]) -> List[Tuple[str, ...]]:
        """
        Generate valid combinations of currency pairs
        
        Args:
            available_pairs: List of available currency pairs
            
        Returns:
            List of valid combinations
        """
        logger.info(f"Generating combinations from {len(available_pairs)} pairs")
        
        valid_combinations = []
        
        # Generate all possible combinations of PORTFOLIO_SIZE pairs
        for combo in combinations(available_pairs, PORTFOLIO_SIZE):
            # Check currency distribution constraints (existing)
            is_valid, _ = validate_portfolio_distribution(combo)

            # Check total currency occurrence constraint (new)
            if is_valid:
                is_valid = self._validate_currency_total_occurrence(combo)

            if is_valid:
                valid_combinations.append(combo)
        
        logger.info(f"Found {len(valid_combinations)} valid combinations")
        return valid_combinations

    def _validate_currency_total_occurrence(self, pairs: Tuple[str, ...]) -> bool:
        """
        Validate that no currency appears more than MAX_CURRENCY_TOTAL_OCCURRENCE times
        total (as base or quote) in a portfolio

        Args:
            pairs: Tuple of currency pairs to validate

        Returns:
            True if valid, False otherwise
        """
        currency_counts = {}

        for pair in pairs:
            if len(pair) != 6:
                continue  # Skip invalid pairs

            base_currency = pair[:3]
            quote_currency = pair[3:6]

            # Count total occurrences for each currency
            currency_counts[base_currency] = currency_counts.get(base_currency, 0) + 1
            currency_counts[quote_currency] = currency_counts.get(quote_currency, 0) + 1

        # Check if any currency exceeds the total occurrence limit
        for currency, count in currency_counts.items():
            if count > MAX_CURRENCY_TOTAL_OCCURRENCE:
                return False

        return True

    def _optimize_strategy(self,
                          log_returns: pd.DataFrame,
                          combinations: List[Tuple[str, ...]],
                          strategy: str,
                          max_portfolios: int) -> List[Portfolio]:
        """
        Optimize portfolios for a specific strategy using two-stage optimization

        Args:
            log_returns: DataFrame with log returns
            combinations: List of valid combinations
            strategy: Optimization strategy
            max_portfolios: Maximum number of portfolios to return

        Returns:
            List of optimized portfolios
        """
        if ENABLE_TWO_STAGE_OPTIMIZATION:
            return self._two_stage_optimize_strategy(log_returns, combinations, strategy, max_portfolios)
        else:
            return self._single_stage_optimize_strategy(log_returns, combinations, strategy, max_portfolios)

    def _two_stage_optimize_strategy(self,
                                   log_returns: pd.DataFrame,
                                   combinations: List[Tuple[str, ...]],
                                   strategy: str,
                                   max_portfolios: int) -> List[Portfolio]:
        """
        Two-stage optimization: fast pre-screening + refined optimization

        Stage 1: Fast optimization with low iterations to filter out poor combinations
        Stage 2: High-precision optimization on the best candidates from stage 1
        """
        logger.info(f"Starting two-stage optimization for {strategy}")
        logger.info(f"Stage 1: Fast screening of {len(combinations)} combinations")

        # Prepare data for multiprocessing (avoid pickling DataFrame directly)
        log_returns_data = {
            'data': log_returns.values.tolist(),
            'index': log_returns.index.strftime('%Y-%m-%d %H:%M:%S').tolist(),
            'columns': log_returns.columns.tolist()
        }

        # Stage 1: Fast pre-screening
        stage1_combinations = combinations[:min(len(combinations), 2000)]  # Increased limit for stage 1

        if USE_JOBLIB_MULTIPROCESSING:
            parallel_executor = get_parallel_executor()
            if parallel_executor:
                logger.info(f"Using joblib multiprocessing with {get_adaptive_n_jobs()} cores")
                stage1_results = parallel_executor(
                    delayed(optimize_combination_standalone)(
                        log_returns_data, combo, strategy, STAGE1_MAX_ITERATIONS, STAGE1_TOLERANCE
                    ) for combo in stage1_combinations
                )
            else:
                # Fallback to sequential processing
                stage1_results = [
                    optimize_combination_standalone(log_returns_data, combo, strategy,
                                                  STAGE1_MAX_ITERATIONS, STAGE1_TOLERANCE)
                    for combo in stage1_combinations
                ]
        else:
            # Use legacy ProcessPoolExecutor
            stage1_results = []
            with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
                future_to_combo = {
                    executor.submit(self._optimize_combination, log_returns, combo, strategy,
                                  max_iter=STAGE1_MAX_ITERATIONS, tolerance=STAGE1_TOLERANCE): combo
                    for combo in stage1_combinations
                }

                for future in as_completed(future_to_combo):
                    try:
                        portfolio = future.result()
                        if portfolio is not None:
                            stage1_results.append({
                                'pairs': portfolio.pairs,
                                'weights': portfolio.weights,
                                'strategy': portfolio.strategy,
                                'metrics': portfolio.metrics
                            })
                    except Exception as e:
                        logger.debug(f"Stage 1 optimization failed: {str(e)}")

        # Convert results to Portfolio objects
        stage1_portfolios = []
        for result in stage1_results:
            if result is not None:
                portfolio = Portfolio(result['pairs'], result['weights'], result['strategy'])
                portfolio.add_metrics(result['metrics'])
                stage1_portfolios.append(portfolio)

        logger.info(f"Stage 1 completed: {len(stage1_portfolios)} successful optimizations")

        if not stage1_portfolios:
            logger.warning("No successful optimizations in stage 1")
            return []

        # Sort and select top combinations for stage 2
        stage1_portfolios = self._sort_portfolios_by_strategy(stage1_portfolios, strategy)
        top_count = max(1, int(len(stage1_portfolios) * STAGE1_TOP_COMBINATIONS_RATIO))
        top_combinations = [tuple(p.pairs) for p in stage1_portfolios[:top_count]]

        logger.info(f"Stage 2: Refining top {len(top_combinations)} combinations with high precision")

        # Stage 2: High-precision optimization on best candidates
        if USE_JOBLIB_MULTIPROCESSING:
            parallel_executor = get_parallel_executor()
            if parallel_executor:
                stage2_results = parallel_executor(
                    delayed(optimize_combination_standalone)(
                        log_returns_data, combo, strategy, STAGE2_MAX_ITERATIONS, STAGE2_TOLERANCE
                    ) for combo in top_combinations
                )
            else:
                # Fallback to sequential processing
                stage2_results = [
                    optimize_combination_standalone(log_returns_data, combo, strategy,
                                                  STAGE2_MAX_ITERATIONS, STAGE2_TOLERANCE)
                    for combo in top_combinations
                ]
        else:
            # Use legacy ProcessPoolExecutor
            stage2_results = []
            with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
                future_to_combo = {
                    executor.submit(self._optimize_combination, log_returns, combo, strategy,
                                  max_iter=STAGE2_MAX_ITERATIONS, tolerance=STAGE2_TOLERANCE): combo
                    for combo in top_combinations
                }

                for future in as_completed(future_to_combo):
                    try:
                        portfolio = future.result()
                        if portfolio is not None:
                            stage2_results.append({
                                'pairs': portfolio.pairs,
                                'weights': portfolio.weights,
                                'strategy': portfolio.strategy,
                                'metrics': portfolio.metrics
                            })
                    except Exception as e:
                        logger.debug(f"Stage 2 optimization failed: {str(e)}")

        # Convert results to Portfolio objects
        stage2_portfolios = []
        for result in stage2_results:
            if result is not None:
                portfolio = Portfolio(result['pairs'], result['weights'], result['strategy'])
                portfolio.add_metrics(result['metrics'])
                stage2_portfolios.append(portfolio)

        logger.info(f"Stage 2 completed: {len(stage2_portfolios)} refined portfolios")

        # Sort final results and return best portfolios
        final_portfolios = self._sort_portfolios_by_strategy(stage2_portfolios, strategy)
        return final_portfolios[:max_portfolios]

    def _single_stage_optimize_strategy(self,
                                      log_returns: pd.DataFrame,
                                      combinations: List[Tuple[str, ...]],
                                      strategy: str,
                                      max_portfolios: int) -> List[Portfolio]:
        """
        Single-stage optimization (legacy method) with joblib support
        """
        # Prepare data for multiprocessing
        log_returns_data = {
            'data': log_returns.values.tolist(),
            'index': log_returns.index.strftime('%Y-%m-%d %H:%M:%S').tolist(),
            'columns': log_returns.columns.tolist()
        }

        limited_combinations = combinations[:min(len(combinations), 1000)]  # Limit for performance

        if USE_JOBLIB_MULTIPROCESSING:
            parallel_executor = get_parallel_executor()
            if parallel_executor:
                logger.info(f"Single-stage optimization using joblib with {get_adaptive_n_jobs()} cores")
                results = parallel_executor(
                    delayed(optimize_combination_standalone)(
                        log_returns_data, combo, strategy, MAX_ITERATIONS, OPTIMIZATION_TOLERANCE
                    ) for combo in limited_combinations
                )
            else:
                # Fallback to sequential processing
                results = [
                    optimize_combination_standalone(log_returns_data, combo, strategy,
                                                  MAX_ITERATIONS, OPTIMIZATION_TOLERANCE)
                    for combo in limited_combinations
                ]
        else:
            # Use legacy ProcessPoolExecutor
            results = []
            with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
                future_to_combo = {
                    executor.submit(self._optimize_combination, log_returns, combo, strategy): combo
                    for combo in limited_combinations
                }

                for future in as_completed(future_to_combo):
                    try:
                        portfolio = future.result()
                        if portfolio is not None:
                            results.append({
                                'pairs': portfolio.pairs,
                                'weights': portfolio.weights,
                                'strategy': portfolio.strategy,
                                'metrics': portfolio.metrics
                            })
                    except Exception as e:
                        logger.debug(f"Optimization failed: {str(e)}")

        # Convert results to Portfolio objects
        portfolios = []
        for result in results:
            if result is not None:
                portfolio = Portfolio(result['pairs'], result['weights'], result['strategy'])
                portfolio.add_metrics(result['metrics'])
                portfolios.append(portfolio)

        # Sort portfolios by the strategy's primary metric
        portfolios = self._sort_portfolios_by_strategy(portfolios, strategy)
        return portfolios[:max_portfolios]

    def _sort_portfolios_by_strategy(self, portfolios: List[Portfolio], strategy: str) -> List[Portfolio]:
        """Sort portfolios by the strategy's primary metric"""
        if strategy == 'max_sharpe':
            portfolios.sort(key=lambda p: p.metrics.get('sharpe_ratio', -np.inf), reverse=True)
        elif strategy == 'max_sortino':
            portfolios.sort(key=lambda p: p.metrics.get('sortino_ratio', -np.inf), reverse=True)
        elif strategy == 'max_omega':
            portfolios.sort(key=lambda p: p.metrics.get('omega_ratio', -np.inf), reverse=True)
        elif strategy == 'max_calmar':
            portfolios.sort(key=lambda p: p.metrics.get('calmar_ratio', -np.inf), reverse=True)
        elif strategy == 'min_variance':
            portfolios.sort(key=lambda p: p.metrics.get('volatility', np.inf))

        return portfolios
    
    def _optimize_combination(self,
                            log_returns: pd.DataFrame,
                            combination: Tuple[str, ...],
                            strategy: str,
                            max_iter: Optional[int] = None,
                            tolerance: Optional[float] = None) -> Optional[Portfolio]:
        """
        Optimize weights for a specific combination of pairs

        Args:
            log_returns: DataFrame with log returns
            combination: Tuple of currency pairs
            strategy: Optimization strategy
            max_iter: Maximum iterations (default: from config)
            tolerance: Optimization tolerance (default: from config)

        Returns:
            Optimized Portfolio object or None if failed
        """
        # Use provided parameters or defaults from config
        max_iterations = max_iter if max_iter is not None else MAX_ITERATIONS
        opt_tolerance = tolerance if tolerance is not None else OPTIMIZATION_TOLERANCE
        try:
            # Extract returns for the combination
            combo_returns = log_returns[list(combination)].dropna()
            
            if len(combo_returns) < 30:  # Need sufficient data
                return None
            
            # Calculate statistics
            mean_returns = combo_returns.mean().values
            cov_matrix = combo_returns.cov().values
            
            # Set up optimization
            n_assets = len(combination)
            initial_weights = np.array([1.0 / n_assets] * n_assets)
            
            # Bounds for weights
            bounds = tuple((MIN_WEIGHT, MAX_WEIGHT) for _ in range(n_assets))
            
            # Constraint: weights sum to 1
            constraints = [{'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}]
            
            # Choose objective function based on strategy
            if strategy == 'max_sharpe':
                objective = lambda w: -self._calculate_sharpe_ratio(w, mean_returns, cov_matrix, combo_returns)
            elif strategy == 'max_sortino':
                objective = lambda w: -self._calculate_sortino_ratio(w, combo_returns.values)
            elif strategy == 'max_omega':
                objective = lambda w: -self._calculate_omega_ratio(w, combo_returns.values)
            elif strategy == 'max_calmar':
                objective = lambda w: -self._calculate_calmar_ratio(w, combo_returns.values)
            elif strategy == 'min_variance':
                if USE_CVAR_FOR_VOLATILITY:
                    # Minimize CVaR instead of variance
                    objective = lambda w: calculate_var_cvar_numba(combo_returns.dot(w).values, CVAR_CONFIDENCE_LEVEL)[1]
                else:
                    # Minimize variance
                    objective = lambda w: self._calculate_portfolio_variance(w, cov_matrix)
            else:
                logger.warning(f"Unknown strategy: {strategy}")
                return None
            
            # Optimize with configurable parameters
            result = minimize(
                objective,
                initial_weights,
                method=OPTIMIZATION_METHOD,
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': max_iterations, 'ftol': opt_tolerance}
            )

            if not result.success:
                return None

            # Create portfolio with normalized weights
            weights = result.x / np.sum(result.x)  # Normalize weights to sum to 1
            portfolio = Portfolio(list(combination), weights.tolist(), strategy)
            
            # Calculate metrics
            metrics = self._calculate_portfolio_metrics(weights, mean_returns, cov_matrix, combo_returns)
            portfolio.add_metrics(metrics)
            
            # Calculate risk profile
            portfolio_returns = combo_returns.dot(weights)
            risk_profile = self.risk_calculator._calculate_pair_risk_profile(portfolio_returns, 'portfolio')
            if risk_profile:
                portfolio.add_risk_profile(risk_profile)
            
            return portfolio
            
        except Exception as e:
            logger.debug(f"Error optimizing combination {combination}: {str(e)}")
            return None
    
    def _calculate_sharpe_ratio(self, weights: np.ndarray, mean_returns: np.ndarray, cov_matrix: np.ndarray, returns_df: pd.DataFrame = None) -> float:
        """Calculate Sharpe ratio for given weights using configured volatility measure"""
        portfolio_return = np.sum(mean_returns * weights)

        if USE_CVAR_FOR_VOLATILITY and returns_df is not None:
            # Use CVaR as risk measure
            portfolio_returns = returns_df.dot(weights)
            _, portfolio_cvar = calculate_var_cvar_numba(portfolio_returns.values, CVAR_CONFIDENCE_LEVEL)
            portfolio_risk = portfolio_cvar
        else:
            # Use standard deviation as risk measure
            portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
            portfolio_risk = np.sqrt(portfolio_variance)

        if portfolio_risk == 0:
            return 0.0

        return portfolio_return / portfolio_risk
    
    def _calculate_sortino_ratio(self, weights: np.ndarray, returns_matrix: np.ndarray) -> float:
        """Calculate Sortino ratio for given weights"""
        portfolio_returns = returns_matrix.dot(weights)
        mean_return = np.mean(portfolio_returns)

        # Calculate downside deviation
        downside_returns = portfolio_returns[portfolio_returns < 0]
        if len(downside_returns) == 0:
            return float('inf')

        downside_std = np.std(downside_returns)
        if downside_std == 0:
            return float('inf')

        return mean_return / downside_std

    def _calculate_omega_ratio(self, weights: np.ndarray, returns_matrix: np.ndarray, threshold: float = 0.0) -> float:
        """Calculate Omega ratio for given weights"""
        portfolio_returns = returns_matrix.dot(weights)

        # Calculate gains above threshold and losses below threshold
        gains = portfolio_returns[portfolio_returns > threshold] - threshold
        losses = threshold - portfolio_returns[portfolio_returns < threshold]

        # Sum of gains and losses
        total_gains = np.sum(gains) if len(gains) > 0 else 0
        total_losses = np.sum(losses) if len(losses) > 0 else 0

        # Omega ratio = total gains / total losses
        if total_losses == 0:
            return float('inf') if total_gains > 0 else 1.0

        return total_gains / total_losses

    def _calculate_calmar_ratio(self, weights: np.ndarray, returns_matrix: np.ndarray) -> float:
        """Calculate Calmar ratio for given weights"""
        portfolio_returns = returns_matrix.dot(weights)
        mean_return = np.mean(portfolio_returns)

        # Calculate maximum drawdown
        cumulative_returns = np.cumprod(1 + portfolio_returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = abs(np.min(drawdown)) if len(drawdown) > 0 else 0.0

        # Calmar ratio = return / max drawdown
        if max_drawdown == 0:
            return float('inf') if mean_return > 0 else 0.0

        return mean_return / max_drawdown
    
    def _calculate_portfolio_variance(self, weights: np.ndarray, cov_matrix: np.ndarray) -> float:
        """Calculate portfolio variance"""
        return np.dot(weights.T, np.dot(cov_matrix, weights))
    
    def _calculate_portfolio_metrics(self,
                                   weights: np.ndarray,
                                   mean_returns: np.ndarray,
                                   cov_matrix: np.ndarray,
                                   returns_df: pd.DataFrame) -> Dict[str, float]:
        """Calculate comprehensive portfolio metrics"""
        # Basic metrics
        portfolio_return = np.sum(mean_returns * weights)
        portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
        portfolio_std = np.sqrt(portfolio_variance)

        # Portfolio return series for additional metrics
        portfolio_returns = returns_df.dot(weights)

        # Choose volatility measure based on configuration
        if USE_CVAR_FOR_VOLATILITY:
            # Use CVaR as volatility measure
            _, portfolio_cvar = calculate_var_cvar_numba(portfolio_returns.values, CVAR_CONFIDENCE_LEVEL)
            portfolio_volatility = portfolio_cvar
            logger.debug(f"Using CVaR as volatility: {portfolio_cvar:.6f} (std: {portfolio_std:.6f})")
        else:
            # Use standard deviation as volatility measure
            portfolio_volatility = portfolio_std

        # Risk-adjusted metrics (using chosen volatility measure)
        sharpe_ratio = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0.0
        sortino_ratio = self._calculate_sortino_ratio(weights, returns_df.values)
        omega_ratio = self._calculate_omega_ratio(weights, returns_df.values)

        # Calmar ratio
        cumulative_returns = (1 + portfolio_returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = abs(drawdown.min()) if len(drawdown) > 0 else 0.0

        calmar_ratio = portfolio_return / max_drawdown if max_drawdown > 0 else float('inf')

        # Information ratio (using chosen volatility measure)
        information_ratio = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0.0

        return {
            'expected_return': float(portfolio_return),
            'volatility': float(portfolio_volatility),  # Now uses CVaR if configured
            'variance': float(portfolio_variance),
            'sharpe_ratio': float(sharpe_ratio),
            'sortino_ratio': float(sortino_ratio),
            'omega_ratio': float(omega_ratio),
            'calmar_ratio': float(calmar_ratio),
            'information_ratio': float(information_ratio),
            'max_drawdown': float(max_drawdown),
            'std_volatility': float(portfolio_std),  # Keep standard deviation for reference
            'cvar_volatility': float(portfolio_cvar) if USE_CVAR_FOR_VOLATILITY else None
        }
    
    def _select_best_portfolios(self, portfolios: List[Portfolio]) -> List[Portfolio]:
        """
        Select the best portfolios across all strategies with pair appearance limits

        Args:
            portfolios: List of all optimized portfolios

        Returns:
            List of best portfolios with enforced diversity
        """
        if not portfolios:
            return []

        if ENABLE_PAIR_APPEARANCE_LIMIT:
            return self._select_portfolios_with_pair_limits(portfolios)
        else:
            return self._select_portfolios_legacy(portfolios)

    def _select_portfolios_with_pair_limits(self, portfolios: List[Portfolio]) -> List[Portfolio]:
        """
        Select portfolios while enforcing maximum pair appearance limits with strategy priority
        """
        # Group portfolios by strategy and sort them
        strategy_groups = {}
        for portfolio in portfolios:
            if portfolio.strategy not in strategy_groups:
                strategy_groups[portfolio.strategy] = []
            strategy_groups[portfolio.strategy].append(portfolio)

        # Sort each strategy group by their primary metric
        for strategy, strategy_portfolios in strategy_groups.items():
            strategy_portfolios = self._sort_portfolios_by_strategy(strategy_portfolios, strategy)
            strategy_groups[strategy] = strategy_portfolios

        selected_portfolios = []
        pair_appearance_count = {}
        strategies_without_portfolio = []

        # First pass: Try to select best portfolio from each strategy (strict limits)
        logger.info("First pass: Selecting best portfolio from each strategy with strict limits")
        for strategy in OPTIMIZATION_STRATEGIES:
            if strategy in strategy_groups and strategy_groups[strategy]:
                portfolio_added = False
                for portfolio in strategy_groups[strategy]:
                    if self._can_add_portfolio(portfolio, pair_appearance_count):
                        selected_portfolios.append(portfolio)
                        self._update_pair_counts(portfolio, pair_appearance_count)
                        logger.info(f"Added {strategy} portfolio: {portfolio.pairs}")
                        portfolio_added = True
                        break

                if not portfolio_added:
                    strategies_without_portfolio.append(strategy)
                    logger.info(f"Could not add {strategy} portfolio due to pair limits")

        # Second pass: Smart strategy representation (respecting pair limits when possible)
        if strategies_without_portfolio and ENSURE_STRATEGY_REPRESENTATION:
            logger.info(f"Second pass: Smart selection for {len(strategies_without_portfolio)} missing strategies")
            for strategy in strategies_without_portfolio:
                if strategy in strategy_groups and strategy_groups[strategy]:
                    # Try to find a portfolio from this strategy that respects pair limits
                    best_portfolio = None

                    # First, try to find a portfolio that doesn't violate pair limits
                    for portfolio in strategy_groups[strategy]:
                        if self._would_violate_pair_limits(portfolio, pair_appearance_count):
                            continue
                        best_portfolio = portfolio
                        break

                    # If no portfolio respects limits, take the one with least violations
                    if best_portfolio is None:
                        best_portfolio = self._find_least_violating_portfolio(
                            strategy_groups[strategy], pair_appearance_count
                        )
                        logger.warning(f"No {strategy} portfolio respects pair limits, using least violating option")
                    else:
                        logger.info(f"Found {strategy} portfolio that respects pair limits")

                    if best_portfolio:
                        selected_portfolios.append(best_portfolio)
                        self._update_pair_counts(best_portfolio, pair_appearance_count)
                        logger.info(f"Added {strategy} portfolio: {best_portfolio.pairs}")
        elif strategies_without_portfolio:
            logger.warning(f"Skipping {len(strategies_without_portfolio)} strategies due to strict pair limits: {strategies_without_portfolio}")

        # Third pass: Add additional portfolios if we have room and they respect limits
        logger.info("Third pass: Adding additional portfolios within limits")
        all_remaining = []
        for strategy, strategy_portfolios in strategy_groups.items():
            for portfolio in strategy_portfolios:
                if portfolio not in selected_portfolios:
                    all_remaining.append(portfolio)

        # Sort all remaining by overall performance (Sharpe ratio)
        all_remaining.sort(key=lambda p: p.metrics.get('sharpe_ratio', -np.inf), reverse=True)

        # Add more portfolios until we reach the target count
        target_count = max(MIN_PORTFOLIOS, len(OPTIMIZATION_STRATEGIES))
        max_portfolios = target_count * 2  # Allow up to 2x for variety

        for portfolio in all_remaining:
            if len(selected_portfolios) >= max_portfolios:
                break
            if self._can_add_portfolio(portfolio, pair_appearance_count):
                selected_portfolios.append(portfolio)
                self._update_pair_counts(portfolio, pair_appearance_count)
                logger.info(f"Added additional portfolio: {portfolio.strategy} - {portfolio.pairs}")

        logger.info(f"Final selection: {len(selected_portfolios)} portfolios")
        logger.info(f"Strategies represented: {[p.strategy for p in selected_portfolios]}")
        logger.info(f"Pair appearance counts: {dict(sorted(pair_appearance_count.items()))}")

        return selected_portfolios

    def _select_portfolios_legacy(self, portfolios: List[Portfolio]) -> List[Portfolio]:
        """
        Legacy portfolio selection without pair limits (original method)
        """
        # Group portfolios by strategy
        strategy_groups = {}
        for portfolio in portfolios:
            if portfolio.strategy not in strategy_groups:
                strategy_groups[portfolio.strategy] = []
            strategy_groups[portfolio.strategy].append(portfolio)

        # Select best from each strategy
        best_portfolios = []

        for strategy, strategy_portfolios in strategy_groups.items():
            strategy_portfolios = self._sort_portfolios_by_strategy(strategy_portfolios, strategy)
            # Add best portfolio from this strategy
            if strategy_portfolios:
                best_portfolios.append(strategy_portfolios[0])

        # Ensure we have at least MIN_PORTFOLIOS
        if len(best_portfolios) < MIN_PORTFOLIOS:
            # Add more portfolios from the best performing strategy
            all_sorted = sorted(portfolios, key=lambda p: p.metrics.get('sharpe_ratio', -np.inf), reverse=True)

            for portfolio in all_sorted:
                if portfolio not in best_portfolios:
                    best_portfolios.append(portfolio)
                    if len(best_portfolios) >= MIN_PORTFOLIOS:
                        break

        return best_portfolios[:MIN_PORTFOLIOS * 2]  # Return up to 2x minimum for variety

    def _can_add_portfolio(self, portfolio: Portfolio, pair_counts: Dict[str, int]) -> bool:
        """
        Check if a portfolio can be added without exceeding pair appearance limits
        """
        for pair in portfolio.pairs:
            if pair_counts.get(pair, 0) >= MAX_PAIR_APPEARANCES:
                return False
        return True

    def _update_pair_counts(self, portfolio: Portfolio, pair_counts: Dict[str, int]) -> None:
        """
        Update pair appearance counts after adding a portfolio
        """
        for pair in portfolio.pairs:
            pair_counts[pair] = pair_counts.get(pair, 0) + 1

    def _would_violate_pair_limits(self, portfolio: Portfolio, pair_counts: Dict[str, int]) -> bool:
        """
        Check if adding this portfolio would violate pair appearance limits
        """
        if not ENABLE_PAIR_APPEARANCE_LIMIT:
            return False

        for pair in portfolio.pairs:
            current_count = pair_counts.get(pair, 0)
            if current_count >= MAX_PAIR_APPEARANCES:
                return True
        return False

    def _find_least_violating_portfolio(self, portfolios: List[Portfolio], pair_counts: Dict[str, int]) -> Portfolio:
        """
        Find the portfolio that violates pair limits the least
        """
        if not portfolios:
            return None

        best_portfolio = None
        min_violations = float('inf')

        for portfolio in portfolios:
            violations = 0
            for pair in portfolio.pairs:
                current_count = pair_counts.get(pair, 0)
                if current_count >= MAX_PAIR_APPEARANCES:
                    violations += (current_count + 1 - MAX_PAIR_APPEARANCES)

            if violations < min_violations:
                min_violations = violations
                best_portfolio = portfolio

        return best_portfolio
    
    def validate_portfolio(self, portfolio: Portfolio) -> Tuple[bool, str]:
        """
        Validate a portfolio against all constraints
        
        Args:
            portfolio: Portfolio to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check portfolio size
        if len(portfolio.pairs) != PORTFOLIO_SIZE:
            return False, f"Portfolio must have exactly {PORTFOLIO_SIZE} pairs"
        
        # Check currency distribution
        is_valid, message = validate_portfolio_distribution(portfolio.pairs)
        if not is_valid:
            return False, message
        
        # Check weights
        if len(portfolio.weights) != len(portfolio.pairs):
            return False, "Number of weights must match number of pairs"
        
        # Check weight bounds
        for weight in portfolio.weights:
            if not (MIN_WEIGHT <= abs(weight) <= MAX_WEIGHT):
                return False, f"Weight {weight} outside bounds [{MIN_WEIGHT}, {MAX_WEIGHT}]"
        
        # Check weight sum (should be close to 1)
        weight_sum = sum(abs(w) for w in portfolio.weights)
        if abs(weight_sum - 1.0) > 0.01:
            return False, f"Absolute weights sum to {weight_sum}, should be 1.0"
        
        return True, "Portfolio is valid"

    def _filter_combinations_for_diversification(self,
                                               combinations: List[Tuple[str, ...]],
                                               used_pairs_weights: Dict[str, float]) -> List[Tuple[str, ...]]:
        """
        Filter combinations to enforce diversification across strategies

        Args:
            combinations: List of valid pair combinations
            used_pairs_weights: Dictionary of pairs and their maximum weights from previous strategies

        Returns:
            Filtered list of combinations that respect diversification constraints
        """
        filtered_combinations = []

        for combination in combinations:
            # Check if this combination violates diversification constraints
            violates_diversification = False

            for pair in combination:
                if pair in used_pairs_weights:
                    # This pair was used significantly in a previous strategy
                    # We need to ensure it won't dominate this strategy too
                    if used_pairs_weights[pair] > config.DIVERSIFICATION_THRESHOLD:
                        # This pair had significant weight before, limit its potential impact
                        violates_diversification = True
                        break

            if not violates_diversification:
                filtered_combinations.append(combination)

        # If filtering is too restrictive, allow some combinations but log warning
        if len(filtered_combinations) < 100:  # Minimum viable combinations
            logger.warning(f"Diversification filtering too restrictive ({len(filtered_combinations)} combinations), relaxing constraints")
            # Return original combinations but log that diversification may be limited
            return combinations[:1000]  # Limit to reasonable number for performance

        return filtered_combinations

    def _update_used_pairs_weights(self, portfolio: Portfolio, used_pairs_weights: Dict[str, float]):
        """
        Update the tracking of used pairs and their weights

        Args:
            portfolio: The best portfolio from the current strategy
            used_pairs_weights: Dictionary to update with pair weights
        """
        for pair, weight in zip(portfolio.pairs, portfolio.weights):
            abs_weight = abs(weight)
            # Only track pairs with significant weights
            if abs_weight >= config.DIVERSIFICATION_THRESHOLD:
                # Store the maximum weight this pair has achieved across strategies
                if pair not in used_pairs_weights or abs_weight > used_pairs_weights[pair]:
                    used_pairs_weights[pair] = abs_weight
                    logger.debug(f"Tracking pair {pair} with weight {abs_weight:.3f} for diversification")


# Utility functions
def create_equal_weight_portfolio(pairs: List[str], strategy: str = 'equal_weight') -> Portfolio:
    """Create an equal-weight portfolio"""
    if len(pairs) != PORTFOLIO_SIZE:
        raise ValueError(f"Must provide exactly {PORTFOLIO_SIZE} pairs")
    
    weights = [1.0 / PORTFOLIO_SIZE] * PORTFOLIO_SIZE
    return Portfolio(pairs, weights, strategy)


def combine_portfolios(portfolio1: Portfolio, portfolio2: Portfolio, weight1: float = 0.5) -> Portfolio:
    """Combine two portfolios with specified weights"""
    if set(portfolio1.pairs) != set(portfolio2.pairs):
        raise ValueError("Portfolios must have the same pairs")
    
    # Combine weights
    combined_weights = []
    for i in range(len(portfolio1.pairs)):
        combined_weight = weight1 * portfolio1.weights[i] + (1 - weight1) * portfolio2.weights[i]
        combined_weights.append(combined_weight)
    
    # Normalize weights
    weight_sum = sum(abs(w) for w in combined_weights)
    combined_weights = [w / weight_sum for w in combined_weights]
    
    return Portfolio(portfolio1.pairs, combined_weights, f'combined_{portfolio1.strategy}_{portfolio2.strategy}')


if __name__ == "__main__":
    # Test the portfolio optimizer
    logging.basicConfig(level=logging.INFO)
    
    print("Testing Portfolio Optimizer...")
    
    # Create sample data
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=500, freq='15T')
    
    pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'NZDUSD', 'EURGBP', 'EURJPY']
    sample_returns = pd.DataFrame({
        pair: np.random.normal(0.0001, 0.005, len(dates))
        for pair in pairs
    }, index=dates)
    
    # Test the optimizer
    optimizer = PortfolioOptimizer()
    
    # Optimize portfolios
    portfolios = optimizer.optimize_portfolios(sample_returns)
    print(f"✓ Optimized {len(portfolios)} portfolios")
    
    # Display results
    for i, portfolio in enumerate(portfolios):
        print(f"\nPortfolio {i+1} ({portfolio.strategy}):")
        print(f"  Pairs: {portfolio.pairs}")
        print(f"  Weights: {[f'{w:.3f}' for w in portfolio.weights]}")
        print(f"  Sharpe: {portfolio.metrics.get('sharpe_ratio', 'N/A'):.3f}")
        print(f"  Volatility: {portfolio.metrics.get('volatility', 'N/A'):.4f}")
        
        # Validate portfolio
        is_valid, message = optimizer.validate_portfolio(portfolio)
        print(f"  Valid: {is_valid} - {message}")
