# Dispersion Breakout Portfolio Strategy (SPECS.md)

---

## 1  Objective

Trade intraday or day‑long trends in major‑FX baskets by exploiting **dispersion breakouts**— sudden expansions in cross‑sectional volatility that often precede directional moves.

- Enter **within ≤ 30 minutes** of a qualified dispersion spike (red‑box events).
- Hold positions for **several hours** (same‑day) or until the trend/volatility regime ends.
- Allocate via pre‑computed MPT portfolios (<PERSON>, Sortino, Calmar, Omega, Min V<PERSON> …).

---

## 2  Signal Detection

| Step | Rule                    | Default Parameter                    |
| ---- | ----------------------- | ------------------------------------ |
| 2.1  | **CSSD threshold ≥ T₁** | 95‑th pct. of last N mins (N = 240)  |
| 2.2  | **ROC filter ≥ T₂**     | ΔCSSD ≥ 2 σ within 15 mins           |
| 2.3  | **Persistence**         | CSSD stays > T₁ for ≥ 3 bars         |
| 2.4  | **Pre‑quiet check**     | Previous 60 mins CSSD < 50‑th pct.   |
| 2.5  | **Trend confirm**       | ADX(14) > 25 **and rising**          |
| 2.6  | **News filter**         | Skip if major scheduled news ±2 mins |

---

## 3  Directional Alignment

1. **Rank normalized returns** of all 28 pairs at trigger time.
2. Identify dominant currency ‑‑ at least **3 pairs** with that currency in the same direction & |norm\_ret| > 1 σ.
3. Require cluster correlation > 0.6 (optional).
4. Build a **directional basket** (e.g. long USD vs majors) consistent with step 2.

---

## 4  Portfolio Selection & Rebalance

| Condition                     | Portfolio Choice               | Rationale                           |
| ----------------------------- | ------------------------------ | ----------------------------------- |
| Valid breakout & clear leader | **Max Sharpe / Max Sortino**   | overweight momentum assets          |
| Choppy breakout but wide vol  | **Omega**                      | skew toward higher upside potential |
| Vol spike but mixed direction | **Min Var** (skip trade often) | defensive—usually no entry          |

- Execute weight vector **within 30 min** of trigger.
- Use market orders or staggered limits; priority = full allocation before move matures.

---

## 5  Execution & Risk

- **Position sizing:** \( lot_i = W_i · E / (ATR_i · K)\), where *E* = equity, *K* risk scaling.
- **Initial Stop:** just inside prior range or *Stop\_i = Entry\_i ± 1.2 × ATR\_i* (direction‑specific).
- **Max portfolio VaR:** ≤ 2 % account equity.

---

## 6  Exit Logic

| Exit Trigger                                   | Action                |
| ---------------------------------------------- | --------------------- |
| CSSD contracts below 0.5 × peak **or** < T₁    | Close all / scale‑out |
| Opposite dispersion spike with opposite leader | Flip / exit           |
| Time stop (end‑of‑session)                     | Close residual        |
| Trailing stop hit (Swing Low/High)             | Close affected leg    |

---

## 7  Automation Checklist (MT5)

1. Real‑time feed → compute minute CSSD, ROC, ADX.
2. Evaluate rules 2.1‑2.6 every minute.
3. On signal:
   - Determine dominant currency basket.
   - Load latest portfolio set; select per §4.
   - Rebalance via MT5 bulk order API (weights & lot size).
4. Monitor exit conditions every minute; enforce rule‑based exits.

---

## 8  Parameter Tuning

- **T₁ / CSSD percentile:** back‑test 90‑97 % range.
- **ROC σ‑multiplier:** test 1.5‑3 σ.
- **ADX window:** 14 or 21.

Store tunables in a YAML/JSON config for nightly optimisation.

---

## 9  Back‑Testing Notes

- Use at least **2 years** of minute‑data, include weekends removal.
- Walk‑forward optimise thresholds quarterly; lock portfolios daily.
- Evaluate KPIs: CAGR, Sharpe, max DD, win %, average trade R.

---

## 10  Future Enhancements

- Machine‑learned CSSD regime classifier.
- Vol‑adjusted dynamic portfolio re‑optimisation every 2 h.
- Position‑netting to reduce margin usage.



---

## 11  CSSD Calculation for Directional Baskets

This appendix gives the **precise algorithm** for computing the cross‑sectional standard deviation (CSSD) of any single‑currency basket (e.g. USD‑centric).

### 11.1  Inputs

| Symbol set   | Example (USD basket)                                                                               | Note                                              |
| ------------ | -------------------------------------------------------------------------------------------------- | ------------------------------------------------- |
| `PAIRS`      | `[EURUSD, GBPUSD, AUDUSD, NZDUSD, USDJPY, USDCAD, USDCHF]`                                         | Use **all pairs that contain the currency**.      |
| `RETURNS_df` | Minute‑by‑minute **log‑returns** or **pct‑returns** for each pair (columns = pairs, index = time). | Must be **normalized directionally** (see §11.2). |

### 11.2  Directional Normalisation

Because some pairs have the currency as *base* and others as *quote*, normalise so that **positive return = currency strength**.

```python
DIRECTION = {
    # quote‑based -> multiply by −1 so positive  means USD strength
    "EURUSD": -1,
    "GBPUSD": -1,
    "AUDUSD": -1,
    "NZDUSD": -1,
    # base‑based  -> keep sign ( +1 )
    "USDJPY": +1,
    "USDCAD": +1,
    "USDCHF": +1,
}
rets_norm = RETURNS_df.mul(pd.Series(DIRECTION))  # broadcast sign
```

### 11.3  CSSD Formula (per timestep)

```python
mu_t      = rets_norm.mean(axis=1)
cssd_t_sq = ((rets_norm.subtract(mu_t, axis=0))**2).mean(axis=1)
CSSD      = np.sqrt(cssd_t_sq)  # Series indexed by time
```

- `` – cross‑sectional mean return (basket drift).
- `` – mean squared deviation.
- Take **square‑root** to obtain the dispersion in return units.

### 11.4  Rolling / Expanding Variant

For a running measure inside a single day use:

```python
CSSD_roll = rets_norm.groupby(rets_norm.index.date).expanding().std(ddof=1).reset_index(level=0, drop=True)
```

This mirrors the logic in `plotting.create_rolling_dispersion_chart`.

### 11.5  Output

Return a `` or merge into dashboard chart.  Feed `CSSD_roll` into the signal‑detection logic (§2).

### 11.6  Unit Test Snippet

```python
assert not CSSD.isna().all(), "CSSD all-NaN—check pair list or return calc"
assert CSSD.max() < 1.0, "Unusually large dispersion—verify log-return scaling"
```



---

## 12  Prompts for Coder AI — Multi‑Currency CSSD, Variance, IQR

Provide the following **step‑wise prompts** (or code tasks) to the coding assistant so it can implement CSSD, Variance‑from‑Mean, and Inter‑Quartile‑Range (IQR) for **all eight major currencies**: USD, EUR, JPY, GBP, AUD, NZD, CAD, CHF.

### 12.1  Prompt 01 — Generate Basket Definitions

> **Task:** *For each of the eight currencies, create a list of all 6 or 7 major pairs that contain that currency (both base‑ and quote‑side).*  Return a Python `dict` keyed by currency → list of pair symbols.

Example (partial):

```python
BASKETS = build_currency_baskets(["USD","EUR","JPY",...])
# BASKETS["USD"]  -> ["EURUSD","GBPUSD","AUDUSD","NZDUSD","USDJPY","USDCAD","USDCHF"]
```

### 12.2  Prompt 02 — Directional Sign Map

> **Task:** *For a given currency basket, produce a **``** of +1 / ‑1 multipliers so that positive returns mean strength of the focus currency.*  Base‑side pairs get +1, quote‑side pairs get – 1.

### 12.3  Prompt 03 — Compute Per‑Currency Metrics (Loop)

> **Task:** *Loop through each currency in **``** and calculate, per timestamp:*
>
> 1. `mean_t` – cross‑sectional mean of normalised returns.
> 2. `var_t`  – **variance from the mean** (population, i.e. `ddof=0`).
> 3. `cssd_t` – square‑root of variance (already §11).
> 4. `iqr_t`  – **inter‑quartile range** (`Q75 – Q25`) of the normalised returns.
>
> Store each as a `pd.Series` in a results dictionary.

Skeleton:

```python
results = {}
for ccy, pairs in BASKETS.items():
    rets   = RETURNS_df[pairs]
    signs  = sign_map(ccy, pairs)          # Prompt 02
    norm   = rets.mul(signs)

    mean_t = norm.mean(axis=1)
    var_t  = ((norm.subtract(mean_t, axis=0))**2).mean(axis=1)
    cssd_t = np.sqrt(var_t)
    q75    = norm.quantile(0.75, axis=1)
    q25    = norm.quantile(0.25, axis=1)
    iqr_t  = q75 - q25

    results[ccy] = {
        "CSSD": cssd_t,
        "VAR":  var_t,
        "IQR":  iqr_t,
    }
```

### 12.4  Prompt 04 — Plot Secondary Lines

> **Task:** *Extend existing Plotly chart:*  for each currency plot **three lines** – CSSD (primary), VAR, and IQR – using muted styles (e.g. dashed for VAR, dotted for IQR) so they sit behind or beside the main dispersion lines.

Guideline:

```python
fig.add_trace(go.Scatter(x=idx, y=var_t, name=f"{ccy} VAR",  line=dict(width=1, dash="dash")))
fig.add_trace(go.Scatter(x=idx, y=iqr_t, name=f"{ccy} IQR",  line=dict(width=1, dash="dot")))
```

### 12.5  Prompt 05 — Rolling / Expanding Option

> **Task:** *Accept a parameter **``** ∈ {"rolling","expanding"}.  If **``**, apply a **``** stat; if **``**, use the **``** method as in §11.*  Parameterise `W` (e.g. 60 mins).

### 12.6  Prompt 06 — Unit Tests for All Metrics

> **Task:** *Write pytest cases that assert:*
>
> - No NaNs after initial `W` observations.
> - CSSD ≈ √VAR (tolerance < 1e‑8).
> - IQR ≥ 0 always.

---

> 💡 **Save these prompts** in a separate file `coder_prompts_CSSD.md` so your automation pipeline can inject them into your code‑generation agent.

