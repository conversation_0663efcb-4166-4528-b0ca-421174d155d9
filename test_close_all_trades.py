#!/usr/bin/env python3
"""
Test script for the Close All Trades functionality
This script tests the MT5 connector's close_all_positions method
"""

import sys
import logging
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, '.')

from mt5_connector import MT5Connector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_close_all_trades():
    """Test the close all trades functionality"""
    logger.info("=== TESTING CLOSE ALL TRADES FUNCTIONALITY ===")
    
    # Initialize MT5 connector
    mt5_connector = MT5Connector()
    
    try:
        # Test connection
        logger.info("Testing MT5 connection...")
        if not mt5_connector.connect():
            logger.error("Failed to connect to MT5")
            return False
        
        logger.info("✓ MT5 connection successful")
        
        # Get account info
        account_info = mt5_connector.get_account_info()
        if account_info:
            logger.info(f"Account: {account_info['server']}")
            logger.info(f"Balance: {account_info['balance']} {account_info['currency']}")
            logger.info(f"Current Profit: {account_info['profit']} {account_info['currency']}")
        
        # Test close all positions
        logger.info("Testing close_all_positions method...")
        result = mt5_connector.close_all_positions()
        
        logger.info(f"Close all positions result: {result}")
        
        closed_count = result.get("closed", 0)
        failed_count = result.get("failed", 0)
        
        if closed_count > 0:
            logger.info(f"✓ Successfully closed {closed_count} positions")
        if failed_count > 0:
            logger.warning(f"⚠ Failed to close {failed_count} positions")
        if closed_count == 0 and failed_count == 0:
            logger.info("ℹ No open positions found to close")
        
        logger.info("=== TEST COMPLETED ===")
        return True
        
    except Exception as e:
        logger.error(f"Test failed with exception: {str(e)}")
        return False
    
    finally:
        # Disconnect
        mt5_connector.disconnect()
        logger.info("MT5 connection closed")

if __name__ == "__main__":
    print("Matrix QP - Close All Trades Test")
    print("=" * 50)
    
    success = test_close_all_trades()
    
    if success:
        print("\n✓ Test completed successfully")
        sys.exit(0)
    else:
        print("\n✗ Test failed")
        sys.exit(1)