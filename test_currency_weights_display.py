#!/usr/bin/env python3
"""
Test script to verify the currency weights display functionality.
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dispersion_charts import DispersionChartCreator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_currency_weights_display():
    """Test the currency weights calculation and display"""
    
    logger.info("Testing currency weights display...")
    
    # Create test data
    start_time = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    time_index = [start_time + timedelta(minutes=i) for i in range(100)]
    
    # Create normalized returns data for all 28 currency pairs
    currency_pairs = [
        'EURUSD', 'EURGBP', 'EURAUD', 'EURNZD', 'EURCHF', 'EURCAD', 'EURJPY',
        'GBPUSD', 'GBPAUD', 'GBPNZD', 'GBPCHF', 'GBPCAD', 'GBPJPY',
        'AUDUSD', 'AUDNZD', 'AUDCHF', 'AUDCAD', 'AUDJPY',
        'NZDUSD', 'NZDCHF', 'NZDCAD', 'NZDJPY',
        'USDCHF', 'USDCAD', 'USDJPY',
        'CADCHF', 'CADJPY',
        'CHFJPY'
    ]
    
    np.random.seed(42)  # For reproducible results
    
    # Generate correlated returns to simulate realistic currency behavior
    normalized_returns_data = {}
    for pair in currency_pairs:
        # Add some correlation structure
        base_returns = np.random.normal(0, 0.001, len(time_index))
        normalized_returns_data[pair] = base_returns
    
    # Create DataFrame
    normalized_returns_df = pd.DataFrame(normalized_returns_data, index=time_index)
    
    # Test chart creation with weights
    chart_creator = DispersionChartCreator()
    
    logger.info("Creating currency CSSD chart with weights...")
    fig, pair_contributions, currency_weights, sharpe_optimized_allocation = chart_creator.create_currency_cssd_chart(normalized_returns_df)
    
    logger.info(f"Chart created successfully with {len(fig.data)} traces")
    
    # Verify currency weights structure
    logger.info("Verifying currency weights structure...")
    
    currencies = ['USD', 'EUR', 'GBP', 'AUD', 'NZD', 'CAD', 'CHF', 'JPY']
    
    for currency in currencies:
        if currency in currency_weights:
            weights = currency_weights[currency]
            logger.info(f"{currency}: {len(weights)} pairs")
            
            # Verify weights sum to 1 (within tolerance)
            total_weight = sum(weight for _, weight in weights)
            logger.info(f"  Total weight: {total_weight:.6f}")
            
            if abs(total_weight - 1.0) > 0.001:
                logger.warning(f"  ⚠️  Weights don't sum to 1.0 for {currency}")
            else:
                logger.info(f"  ✅ Weights correctly normalized for {currency}")
            
            # Show the pairs and weights
            for pair, weight in weights:
                logger.info(f"    {pair}: {weight:.3f}")
        else:
            logger.warning(f"No weights found for {currency}")
    
    # Test dashboard table creation (simulate)
    logger.info("Testing dashboard table creation...")
    
    try:
        from dashboard import PortfolioDashboard
        
        # Create a mock dashboard instance to test the table creation method
        dashboard = PortfolioDashboard(None, None, None, None)
        
        # Test the table creation method
        table_html = dashboard._create_pair_contributions_table(pair_contributions, currency_weights)
        
        logger.info("✅ Dashboard table created successfully")
        logger.info(f"Table type: {type(table_html)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Dashboard table creation failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_currency_weights_display()
    if success:
        logger.info("✅ All currency weights tests passed!")
    else:
        logger.error("❌ Currency weights tests failed!")
