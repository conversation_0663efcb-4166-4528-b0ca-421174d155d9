"""
Risk Calculator Module for Matrix QP
Calculates comprehensive risk profiles including drawdown, variance, VaR, and CVaR
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from numba import njit

from config import (
    CONFIDENCE_LEVELS, RISK_FREE_RATE, TRADING_DAYS_PER_YEAR,
    MAX_DRAWDOWN_THRESHOLD, VOLATILITY_THRESHOLD
)

# Configure logging
logger = logging.getLogger(__name__)


class RiskCalculator:
    """
    Comprehensive risk calculator for portfolio optimization
    Calculates various risk metrics for individual pairs and portfolios
    """
    
    def __init__(self):
        """Initialize the risk calculator"""
        self.cached_risk_profiles = None
        self.last_calculation_time = None
    
    def calculate_risk_profiles(self, 
                              log_returns: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """
        Calculate comprehensive risk profiles for all currency pairs
        
        Args:
            log_returns: DataFrame with log returns for each pair
            
        Returns:
            Dictionary mapping pair names to risk metrics
        """
        logger.info(f"Calculating risk profiles for {len(log_returns.columns)} pairs")
        
        if log_returns.empty:
            logger.warning("Empty log returns DataFrame provided")
            return {}
        
        risk_profiles = {}
        
        for pair in log_returns.columns:
            try:
                returns = log_returns[pair].dropna()
                
                if len(returns) < 10:  # Need minimum data for meaningful risk calculation
                    logger.warning(f"Insufficient data for risk calculation: {pair}")
                    continue
                
                profile = self._calculate_pair_risk_profile(returns, pair)
                if profile:
                    risk_profiles[pair] = profile
                    
            except Exception as e:
                logger.error(f"Error calculating risk profile for {pair}: {str(e)}")
        
        # Cache the results
        self.cached_risk_profiles = risk_profiles.copy()
        self.last_calculation_time = pd.Timestamp.now()
        
        logger.info(f"Successfully calculated risk profiles for {len(risk_profiles)} pairs")
        return risk_profiles
    
    def _calculate_pair_risk_profile(self, 
                                   returns: pd.Series, 
                                   pair: str) -> Optional[Dict[str, float]]:
        """
        Calculate comprehensive risk metrics for a single pair
        
        Args:
            returns: Series of log returns
            pair: Currency pair name
            
        Returns:
            Dictionary with risk metrics or None if failed
        """
        try:
            # Basic statistics
            mean_return = float(returns.mean())
            volatility = float(returns.std())
            
            # Annualized metrics (assuming 1-minute data)
            periods_per_day = 24 * 60  # 1-minute intervals
            annualized_return = mean_return * periods_per_day * TRADING_DAYS_PER_YEAR
            annualized_volatility = volatility * np.sqrt(periods_per_day * TRADING_DAYS_PER_YEAR)
            
            # Drawdown metrics
            drawdown_metrics = self._calculate_drawdown_metrics(returns)
            
            # VaR and CVaR
            var_cvar_metrics = self._calculate_var_cvar_metrics(returns)
            
            # Higher moments
            skewness = float(returns.skew())
            kurtosis = float(returns.kurtosis())
            
            # Risk-adjusted returns
            sharpe_ratio = (annualized_return - RISK_FREE_RATE) / annualized_volatility if annualized_volatility > 0 else 0
            sortino_ratio = self._calculate_sortino_ratio(returns, RISK_FREE_RATE)
            calmar_ratio = self._calculate_calmar_ratio(returns, drawdown_metrics['max_drawdown'])
            
            # Tail risk metrics
            tail_metrics = self._calculate_tail_risk_metrics(returns)
            
            # Compile all metrics
            risk_profile = {
                # Basic metrics
                'mean_return': mean_return,
                'volatility': volatility,
                'annualized_return': annualized_return,
                'annualized_volatility': annualized_volatility,
                
                # Drawdown metrics
                **drawdown_metrics,
                
                # VaR/CVaR metrics
                **var_cvar_metrics,
                
                # Distribution metrics
                'skewness': skewness,
                'kurtosis': kurtosis,
                
                # Risk-adjusted returns
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
                
                # Tail risk
                **tail_metrics,
                
                # Risk classification
                'risk_level': self._classify_risk_level(annualized_volatility, drawdown_metrics['max_drawdown']),
                'data_points': len(returns)
            }
            
            return risk_profile
            
        except Exception as e:
            logger.error(f"Error in _calculate_pair_risk_profile for {pair}: {str(e)}")
            return None
    
    def _calculate_drawdown_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate drawdown-related metrics"""
        # Calculate cumulative returns
        cumulative_returns = (1 + returns).cumprod()
        
        # Calculate running maximum (peak)
        running_max = cumulative_returns.expanding().max()
        
        # Calculate drawdown
        drawdown = (cumulative_returns - running_max) / running_max
        
        # Metrics
        max_drawdown = float(drawdown.min())
        avg_drawdown = float(drawdown[drawdown < 0].mean()) if (drawdown < 0).any() else 0.0
        
        # Drawdown duration
        drawdown_periods = self._calculate_drawdown_duration(drawdown)
        
        return {
            'max_drawdown': abs(max_drawdown),
            'avg_drawdown': abs(avg_drawdown),
            'max_drawdown_duration': drawdown_periods['max_duration'],
            'avg_drawdown_duration': drawdown_periods['avg_duration'],
            'current_drawdown': abs(float(drawdown.iloc[-1])) if len(drawdown) > 0 else 0.0
        }
    
    def _calculate_drawdown_duration(self, drawdown: pd.Series) -> Dict[str, float]:
        """Calculate drawdown duration statistics"""
        durations = []
        current_duration = 0
        
        for dd in drawdown:
            if dd < 0:
                current_duration += 1
            else:
                if current_duration > 0:
                    durations.append(current_duration)
                    current_duration = 0
        
        # Add final duration if still in drawdown
        if current_duration > 0:
            durations.append(current_duration)
        
        if durations:
            return {
                'max_duration': float(max(durations)),
                'avg_duration': float(np.mean(durations))
            }
        else:
            return {'max_duration': 0.0, 'avg_duration': 0.0}
    
    def _calculate_var_cvar_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate VaR and CVaR for different confidence levels"""
        metrics = {}
        
        for confidence in CONFIDENCE_LEVELS:
            var, cvar = calculate_var_cvar_numba(returns.values, confidence)
            
            metrics[f'var_{int(confidence*100)}'] = var
            metrics[f'cvar_{int(confidence*100)}'] = cvar
        
        return metrics
    
    def _calculate_sortino_ratio(self, returns: pd.Series, risk_free_rate: float = 0.0) -> float:
        """Calculate Sortino ratio (return/downside deviation)"""
        mean_return = returns.mean()
        
        # Calculate downside deviation
        downside_returns = returns[returns < risk_free_rate]
        if len(downside_returns) > 0:
            downside_deviation = downside_returns.std()
            
            # Annualize
            periods_per_day = 24 * 60  # 1-minute intervals
            annualized_return = mean_return * periods_per_day * TRADING_DAYS_PER_YEAR
            annualized_downside_dev = downside_deviation * np.sqrt(periods_per_day * TRADING_DAYS_PER_YEAR)
            
            return (annualized_return - risk_free_rate) / annualized_downside_dev if annualized_downside_dev > 0 else 0
        else:
            return float('inf')  # No downside risk
    
    def _calculate_calmar_ratio(self, returns: pd.Series, max_drawdown: float) -> float:
        """Calculate Calmar ratio (annualized return / max drawdown)"""
        if max_drawdown == 0:
            return float('inf')
        
        periods_per_day = 24 * 60  # 1-minute intervals
        annualized_return = returns.mean() * periods_per_day * TRADING_DAYS_PER_YEAR
        
        return annualized_return / max_drawdown
    
    def _calculate_tail_risk_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate tail risk metrics"""
        sorted_returns = returns.sort_values()
        n = len(sorted_returns)
        
        # Tail ratios
        tail_5_pct = int(0.05 * n)
        tail_1_pct = int(0.01 * n)
        
        metrics = {
            'worst_return': float(sorted_returns.iloc[0]) if n > 0 else 0.0,
            'best_return': float(sorted_returns.iloc[-1]) if n > 0 else 0.0,
            'tail_5pct_mean': float(sorted_returns.iloc[:tail_5_pct].mean()) if tail_5_pct > 0 else 0.0,
            'tail_1pct_mean': float(sorted_returns.iloc[:tail_1_pct].mean()) if tail_1_pct > 0 else 0.0,
        }
        
        # Expected shortfall
        if tail_5_pct > 0:
            metrics['expected_shortfall_5pct'] = abs(metrics['tail_5pct_mean'])
        else:
            metrics['expected_shortfall_5pct'] = 0.0
        
        return metrics
    
    def _classify_risk_level(self, volatility: float, max_drawdown: float) -> str:
        """Classify risk level based on volatility and drawdown"""
        if volatility > VOLATILITY_THRESHOLD or max_drawdown > MAX_DRAWDOWN_THRESHOLD:
            return 'HIGH'
        elif volatility > VOLATILITY_THRESHOLD * 0.6 or max_drawdown > MAX_DRAWDOWN_THRESHOLD * 0.6:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def calculate_portfolio_risk(self, 
                               returns: pd.DataFrame,
                               weights: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate risk metrics for a portfolio
        
        Args:
            returns: DataFrame with returns for each asset
            weights: Dictionary mapping asset names to weights
            
        Returns:
            Dictionary with portfolio risk metrics
        """
        try:
            # Align weights with returns columns
            aligned_weights = []
            aligned_returns = []
            
            for asset in returns.columns:
                if asset in weights:
                    aligned_weights.append(weights[asset])
                    aligned_returns.append(returns[asset])
            
            if not aligned_weights:
                logger.warning("No matching assets found between returns and weights")
                return {}
            
            # Convert to arrays
            weights_array = np.array(aligned_weights)
            returns_matrix = pd.DataFrame(aligned_returns).T
            
            # Calculate portfolio returns
            portfolio_returns = returns_matrix.dot(weights_array)
            
            # Calculate portfolio risk profile
            portfolio_profile = self._calculate_pair_risk_profile(portfolio_returns, 'portfolio')
            
            # Add portfolio-specific metrics
            if portfolio_profile:
                # Correlation metrics
                correlation_matrix = returns_matrix.corr()
                portfolio_profile['avg_correlation'] = float(correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].mean())
                portfolio_profile['max_correlation'] = float(correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].max())
                
                # Diversification ratio
                individual_vol = returns_matrix.std()
                weighted_avg_vol = (individual_vol * np.abs(weights_array)).sum()
                portfolio_vol = portfolio_returns.std()
                portfolio_profile['diversification_ratio'] = weighted_avg_vol / portfolio_vol if portfolio_vol > 0 else 1.0
            
            return portfolio_profile
            
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {str(e)}")
            return {}
    
    def rank_pairs_by_risk(self, 
                          risk_profiles: Optional[Dict[str, Dict[str, float]]] = None,
                          metric: str = 'sharpe_ratio',
                          ascending: bool = False) -> List[Tuple[str, float]]:
        """
        Rank currency pairs by a specific risk metric
        
        Args:
            risk_profiles: Risk profiles dictionary (uses cached if None)
            metric: Risk metric to rank by
            ascending: Whether to sort in ascending order
            
        Returns:
            List of (pair, metric_value) tuples sorted by metric
        """
        if risk_profiles is None:
            if self.cached_risk_profiles is None:
                raise ValueError("No risk profiles available. Calculate risk profiles first.")
            risk_profiles = self.cached_risk_profiles
        
        # Extract metric values
        metric_values = []
        for pair, profile in risk_profiles.items():
            if metric in profile:
                metric_values.append((pair, profile[metric]))
        
        # Sort by metric
        metric_values.sort(key=lambda x: x[1], reverse=not ascending)
        
        return metric_values
    
    def get_risk_summary(self, 
                        risk_profiles: Optional[Dict[str, Dict[str, float]]] = None) -> Dict[str, float]:
        """
        Get summary statistics across all pairs
        
        Args:
            risk_profiles: Risk profiles dictionary (uses cached if None)
            
        Returns:
            Dictionary with summary statistics
        """
        if risk_profiles is None:
            if self.cached_risk_profiles is None:
                raise ValueError("No risk profiles available. Calculate risk profiles first.")
            risk_profiles = self.cached_risk_profiles
        
        if not risk_profiles:
            return {}
        
        # Collect all metrics
        all_metrics = {}
        for pair, profile in risk_profiles.items():
            for metric, value in profile.items():
                if isinstance(value, (int, float)) and not np.isnan(value):
                    if metric not in all_metrics:
                        all_metrics[metric] = []
                    all_metrics[metric].append(value)
        
        # Calculate summary statistics
        summary = {}
        for metric, values in all_metrics.items():
            if values:
                summary[f'{metric}_mean'] = float(np.mean(values))
                summary[f'{metric}_std'] = float(np.std(values))
                summary[f'{metric}_min'] = float(np.min(values))
                summary[f'{metric}_max'] = float(np.max(values))
                summary[f'{metric}_median'] = float(np.median(values))
        
        return summary


# Numba-optimized functions
@njit
def calculate_var_cvar_numba(returns_array: np.ndarray, confidence_level: float = 0.95) -> Tuple[float, float]:
    """
    Numba-optimized VaR and CVaR calculation
    
    Args:
        returns_array: Array of returns
        confidence_level: Confidence level for calculation
        
    Returns:
        Tuple of (VaR, CVaR)
    """
    # Clean the input array (remove NaNs)
    valid_returns = []
    for x in returns_array:
        if x == x:  # Check if not NaN
            valid_returns.append(x)
    
    if len(valid_returns) == 0:
        return 0.0, 0.0
    
    # Sort returns from worst to best
    sorted_returns = np.sort(np.array(valid_returns))
    
    # Find the index at the specified confidence level
    index = int((1 - confidence_level) * len(sorted_returns))
    if index < 0:
        index = 0
    
    # VaR is the loss at this confidence level (negative of return)
    var = -sorted_returns[index]
    
    # CVaR is the average loss beyond VaR
    tail_index = index + 1
    if tail_index > 0 and tail_index <= len(sorted_returns):
        tail_sum = 0.0
        for i in range(tail_index):
            tail_sum += sorted_returns[i]
        cvar = -tail_sum / tail_index
    else:
        cvar = var
    
    return var, cvar


@njit
def fast_drawdown_calculation(cumulative_returns: np.ndarray) -> Tuple[float, float]:
    """
    Fast drawdown calculation using Numba
    
    Args:
        cumulative_returns: Array of cumulative returns
        
    Returns:
        Tuple of (max_drawdown, current_drawdown)
    """
    n = len(cumulative_returns)
    if n == 0:
        return 0.0, 0.0
    
    running_max = cumulative_returns[0]
    max_drawdown = 0.0
    
    for i in range(1, n):
        if cumulative_returns[i] > running_max:
            running_max = cumulative_returns[i]
        
        drawdown = (cumulative_returns[i] - running_max) / running_max
        if drawdown < max_drawdown:
            max_drawdown = drawdown
    
    # Current drawdown
    current_drawdown = (cumulative_returns[-1] - running_max) / running_max
    
    return abs(max_drawdown), abs(current_drawdown)


if __name__ == "__main__":
    # Test the risk calculator
    logging.basicConfig(level=logging.INFO)
    
    print("Testing Risk Calculator...")
    
    # Create sample return data
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=1000, freq='15T')
    
    sample_returns = pd.DataFrame({
        'EURUSD': np.random.normal(0.0001, 0.005, len(dates)),
        'GBPUSD': np.random.normal(0.0002, 0.007, len(dates)),
        'USDJPY': np.random.normal(-0.0001, 0.006, len(dates))
    }, index=dates)
    
    # Test the calculator
    calculator = RiskCalculator()
    
    # Calculate risk profiles
    risk_profiles = calculator.calculate_risk_profiles(sample_returns)
    print(f"✓ Risk profiles calculated for {len(risk_profiles)} pairs")
    
    # Test portfolio risk
    weights = {'EURUSD': 0.4, 'GBPUSD': 0.3, 'USDJPY': 0.3}
    portfolio_risk = calculator.calculate_portfolio_risk(sample_returns, weights)
    print(f"✓ Portfolio risk calculated: {len(portfolio_risk)} metrics")
    
    # Test ranking
    rankings = calculator.rank_pairs_by_risk(risk_profiles, 'sharpe_ratio')
    print(f"✓ Pairs ranked by Sharpe ratio: {len(rankings)} pairs")
    
    # Test summary
    summary = calculator.get_risk_summary(risk_profiles)
    print(f"✓ Risk summary calculated: {len(summary)} summary metrics")
