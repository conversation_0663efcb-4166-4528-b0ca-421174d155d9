"""
Example of how to integrate the Portfolio Entry Warning System into the main dashboard
"""

import dash
from dash import dcc, html, Input, Output, State
import plotly.graph_objects as go
from dispersion_charts import DispersionChartCreator

def add_warning_system_to_dashboard(app, dispersion_calculator, data_loader):
    """
    Example function showing how to add the warning system to your dashboard
    
    Args:
        app: Dash app instance
        dispersion_calculator: DispersionCalculator instance
        data_loader: DataLoader instance
    """
    
    # Add warning system controls to your layout
    warning_system_controls = html.Div([
        html.H4("Portfolio Entry Warning System", className="mb-3"),
        
        html.Div([
            html.Label("Lookback Window (minutes):", className="form-label"),
            dcc.Input(
                id="warning-lookback-window",
                type="number",
                value=240,
                min=60,
                max=480,
                step=30,
                className="form-control"
            )
        ], className="mb-2"),
        
        html.Div([
            html.Label("Threshold Percentile:", className="form-label"),
            dcc.Input(
                id="warning-threshold-percentile",
                type="number",
                value=95.0,
                min=90.0,
                max=99.0,
                step=1.0,
                className="form-control"
            )
        ], className="mb-2"),
        
        html.Div([
            html.Label("ROC Sigma Multiplier:", className="form-label"),
            dcc.Input(
                id="warning-roc-sigma",
                type="number",
                value=2.0,
                min=1.0,
                max=3.0,
                step=0.5,
                className="form-control"
            )
        ], className="mb-2"),
        
        html.Div([
            html.Label("ADX Threshold:", className="form-label"),
            dcc.Input(
                id="warning-adx-threshold",
                type="number",
                value=25.0,
                min=15.0,
                max=40.0,
                step=5.0,
                className="form-control"
            )
        ], className="mb-2"),
        
        html.Div([
            html.Label("Entry Window (minutes):", className="form-label"),
            dcc.Input(
                id="warning-entry-window",
                type="number",
                value=30,
                min=15,
                max=60,
                step=15,
                className="form-control"
            )
        ], className="mb-3"),
        
        html.Button(
            "Update Warning System",
            id="update-warning-system-btn",
            className="btn btn-primary",
            n_clicks=0
        )
    ], className="card-body")
    
    # Add warning system chart container
    warning_system_chart = html.Div([
        html.H4("Portfolio Entry Warning System", className="mb-3"),
        dcc.Graph(
            id="warning-system-chart",
            config={'displayModeBar': True, 'displaylogo': False}
        ),
        html.Div(id="warning-system-status", className="mt-2")
    ], className="card-body")
    
    # Callback for updating the warning system chart
    @app.callback(
        [Output("warning-system-chart", "figure"),
         Output("warning-system-status", "children")],
        [Input("update-warning-system-btn", "n_clicks"),
         Input("time-period-dropdown", "value")],  # Assuming you have a time period selector
        [State("warning-lookback-window", "value"),
         State("warning-threshold-percentile", "value"),
         State("warning-roc-sigma", "value"),
         State("warning-adx-threshold", "value"),
         State("warning-entry-window", "value")]
    )
    def update_warning_system_chart(n_clicks, time_period, lookback_window, 
                                   threshold_percentile, roc_sigma, adx_threshold, 
                                   entry_window):
        """Update the warning system chart based on current data and parameters"""
        
        try:
            # Load current market data
            market_data = data_loader.load_data_for_period(time_period)
            
            if not market_data:
                return go.Figure(), html.Div("No data available", className="alert alert-warning")
            
            # Calculate CSSD and normalized returns
            normalized_returns_ts = dispersion_calculator.calculate_cumulative_returns_since_sod(market_data)
            cssd_series = dispersion_calculator.calculate_dispersion_metric(normalized_returns_ts)
            
            if cssd_series.empty or normalized_returns_ts.empty:
                return go.Figure(), html.Div("Insufficient data for analysis", className="alert alert-warning")
            
            # Create warning system chart
            chart_creator = DispersionChartCreator()
            fig = chart_creator.create_portfolio_entry_warning_chart(
                cssd_series=cssd_series,
                normalized_returns_ts=normalized_returns_ts,
                lookback_window=lookback_window,
                threshold_percentile=threshold_percentile,
                roc_window=15,  # Fixed parameter
                roc_sigma_multiplier=roc_sigma,
                persistence_bars=3,  # Fixed parameter
                pre_quiet_window=60,  # Fixed parameter
                adx_window=14,  # Fixed parameter
                adx_threshold=adx_threshold,
                entry_window_minutes=entry_window
            )
            
            # Create status message
            latest_cssd = cssd_series.iloc[-1] if not cssd_series.empty else 0
            status_message = html.Div([
                html.P(f"Latest CSSD: {latest_cssd:.4f}", className="mb-1"),
                html.P(f"Data points: {len(cssd_series)}", className="mb-1"),
                html.P(f"Time range: {cssd_series.index[0].strftime('%H:%M')} - {cssd_series.index[-1].strftime('%H:%M')}", className="mb-0")
            ], className="alert alert-info")
            
            return fig, status_message
            
        except Exception as e:
            error_message = html.Div(
                f"Error creating warning system chart: {str(e)}", 
                className="alert alert-danger"
            )
            return go.Figure(), error_message
    
    return warning_system_controls, warning_system_chart

# Example of how to add to your main dashboard layout
def create_enhanced_dashboard_layout():
    """Example of enhanced dashboard layout with warning system"""
    
    return html.Div([
        # Existing dashboard components
        html.Div([
            html.H1("Matrix QP - Enhanced with Warning System"),
            # ... your existing components
        ]),
        
        # Add warning system section
        html.Div([
            html.H2("Portfolio Entry Warning System"),
            
            # Controls and chart side by side
            html.Div([
                # Controls column
                html.Div([
                    html.Div(id="warning-system-controls")
                ], className="col-md-3"),
                
                # Chart column
                html.Div([
                    html.Div(id="warning-system-chart-container")
                ], className="col-md-9")
            ], className="row")
        ], className="container-fluid mt-4"),
        
        # ... rest of your dashboard
    ])

# Usage example
if __name__ == "__main__":
    # This is just an example - you would integrate this into your actual dashboard
    print("This is an integration example.")
    print("Add the warning_system_controls and warning_system_chart to your dashboard layout.")
    print("Use the callback function to update the chart based on user inputs.")
    print("The warning system will help identify optimal portfolio entry timing.")
