# Task Context: Redesign "Close All Positions" Button

## Goal
Relocate and redesign the "Close All MT5 Positions" button based on user feedback and the provided image.

## User Feedback
"Can you place it there and make it prettier?"

## Requirements
1.  **Relocate the Button:** Move the "Close All MT5 Positions" button from its current position to below the "Account Info" box, as indicated by the red arrow in the user-provided image.
2.  **Redesign the Button:**
    *   Make the button visually appealing and consistent with the existing theme.
    *   Consider a design that clearly indicates a critical action (e.g., using a distinct color like red or a prominent style).
    *   The button should span a significant width of the container it's placed in.

## Key Files
*   [`dashboard.py`](dashboard.py): Contains the button's layout definition.
*   [`assets/style.css`](assets/style.css): Contains the styling for the UI elements.

## Reference Image
The user has provided an image showing the desired location for the button. The developer must refer to this image to correctly place the button.