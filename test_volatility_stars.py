#!/usr/bin/env python3
"""
Test script for volatility signal stars on Rolling Dispersion of CSSD by Currency chart
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from dispersion_charts import DispersionChartCreator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data_with_volatility_signals():
    """
    Create test data with clear volatility signals that should trigger stars
    """
    # Create 8 hours of minute data
    start_time = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    time_index = pd.date_range(start=start_time, periods=480, freq='1min')
    
    # Major currency pairs
    pairs = [
        'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDCHF', 'USDJPY',
        'EURGBP', 'EURAUD', 'EURNZD', 'EURCAD', 'EURCHF', 'EURJPY',
        'GBPAUD', 'GBPNZD', 'GBPCAD', 'GBPCHF', 'GBPJPY',
        'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY',
        'NZDCAD', 'NZDCHF', 'NZDJPY',
        'CADCHF', 'CADJPY',
        'CHFJPY'
    ]
    
    # Create base random data (low volatility baseline)
    np.random.seed(42)
    base_data = np.random.normal(0, 0.0002, (len(time_index), len(pairs)))  # Very low volatility
    
    # Add specific volatility signal patterns
    signal_times = [120, 240, 360]  # At 2 hours, 4 hours, 6 hours
    
    for signal_time in signal_times:
        if signal_time < len(time_index):
            # Create coordinated USD movement (multiple USD pairs moving together)
            usd_pairs = [p for p in pairs if 'USD' in p]
            
            # Create high volatility spike that persists for 5+ bars
            for i in range(5):  # 5 bars of persistence
                if signal_time + i < len(time_index):
                    # Make USD pairs move in unison with high correlation
                    usd_movement = np.random.normal(0, 0.008, 1)[0]  # Strong movement
                    
                    for pair in usd_pairs:
                        pair_idx = pairs.index(pair)
                        # Apply directional consistency
                        if pair.startswith('USD'):
                            base_data[signal_time + i, pair_idx] = usd_movement
                        else:
                            base_data[signal_time + i, pair_idx] = -usd_movement
    
    # Convert to cumulative returns
    cumulative_data = np.cumsum(base_data, axis=0)
    
    # Create DataFrame
    df = pd.DataFrame(cumulative_data, index=time_index, columns=pairs)
    
    logger.info(f"Created test data with {len(pairs)} pairs and {len(time_index)} time points")
    logger.info(f"Added volatility signals at times: {signal_times}")
    
    return df

def main():
    logger.info("Starting Volatility Signal Stars Test...")
    
    # Create test data with clear volatility signals
    test_data = create_test_data_with_volatility_signals()
    
    # Initialize dispersion charts
    charts = DispersionChartCreator()
    
    # Create rolling dispersion chart
    logger.info("Creating rolling dispersion chart with volatility signal stars...")
    
    # Calculate rolling dispersion for the test
    from dispersion_calculator import DispersionCalculator
    calc = DispersionCalculator()

    # Calculate normalized returns
    normalized_returns = test_data.diff().fillna(0)

    # Calculate rolling dispersion using the existing method
    rolling_dispersion_df = calc.calculate_rolling_dispersion(normalized_returns)
    
    # Create the chart
    result = charts.create_rolling_dispersion_chart(
        cumulative_returns_ts=test_data,
        rolling_dispersion_df=rolling_dispersion_df,
        show_adx=True
    )
    
    fig = result['figure']
    
    # Count annotations (stars)
    annotation_count = len(fig.layout.annotations) if hasattr(fig.layout, 'annotations') and fig.layout.annotations else 0
    
    logger.info(f"Chart created with {len(fig.data)} traces")
    logger.info(f"Chart created with {annotation_count} star annotations")
    
    # Save chart for inspection
    try:
        fig.write_html("volatility_stars_test.html")
        logger.info("Chart saved as 'volatility_stars_test.html'")
    except Exception as e:
        logger.warning(f"Could not save chart: {e}")
    
    logger.info("✅ Volatility signal stars test completed!")
    
    if annotation_count > 0:
        logger.info(f"✅ Volatility signal stars are working! Found {annotation_count} star annotations.")
    else:
        logger.warning("⚠️ No star annotations found. May need to adjust signal detection sensitivity.")

if __name__ == "__main__":
    main()
