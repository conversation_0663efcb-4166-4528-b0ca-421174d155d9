"""
Weight Management Module for Matrix QP
Handles weight calculation, normalization, and direction assignment based on log returns
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from numba import njit

from config import WEIGHT_TOLERANCE, MIN_WEIGHT, MAX_WEIGHT

# Configure logging
logger = logging.getLogger(__name__)


class WeightManager:
    """
    Advanced weight management system for portfolio optimization
    Handles absolute weight normalization and negative weight assignment
    """
    
    def __init__(self):
        """Initialize the weight manager"""
        self.last_normalization_time = None
        self.cached_weights = None
    
    def normalize_absolute_weights(self, 
                                 weights: Dict[str, float],
                                 target_sum: float = 1.0) -> Dict[str, float]:
        """
        Normalize weights so that the sum of absolute values equals target_sum
        
        Args:
            weights: Dictionary mapping asset names to weights
            target_sum: Target sum for absolute weights (default: 1.0)
            
        Returns:
            Dictionary with normalized weights
        """
        if not weights:
            logger.warning("Empty weights dictionary provided")
            return {}
        
        # Calculate current sum of absolute weights
        current_sum = sum(abs(weight) for weight in weights.values())
        
        if current_sum == 0:
            logger.warning("All weights are zero, cannot normalize")
            return weights
        
        # Calculate normalization factor
        normalization_factor = target_sum / current_sum
        
        # Apply normalization while preserving signs
        normalized_weights = {
            asset: weight * normalization_factor
            for asset, weight in weights.items()
        }
        
        # Verify normalization
        new_sum = sum(abs(weight) for weight in normalized_weights.values())
        if abs(new_sum - target_sum) > WEIGHT_TOLERANCE:
            logger.warning(f"Normalization error: sum = {new_sum}, target = {target_sum}")
        
        logger.debug(f"Normalized weights: sum of absolute values = {new_sum:.6f}")
        return normalized_weights
    
    def assign_weight_directions(self, 
                               base_weights: Dict[str, float],
                               log_returns: pd.DataFrame,
                               lookback_periods: Optional[int] = None) -> Dict[str, float]:
        """
        Assign weight directions based on cumulative log returns
        Negative cumulative returns result in negative weights (short positions)
        
        Args:
            base_weights: Dictionary with absolute weight values
            log_returns: DataFrame with log returns for each asset
            lookback_periods: Number of periods to look back (None = all available)
            
        Returns:
            Dictionary with directional weights
        """
        if not base_weights:
            logger.warning("Empty base weights provided")
            return {}
        
        directional_weights = {}
        
        for asset, abs_weight in base_weights.items():
            if asset not in log_returns.columns:
                logger.warning(f"Asset {asset} not found in log returns data")
                directional_weights[asset] = abs_weight  # Default to positive
                continue
            
            # Get returns for the asset
            asset_returns = log_returns[asset].dropna()
            
            if len(asset_returns) == 0:
                logger.warning(f"No return data for asset {asset}")
                directional_weights[asset] = abs_weight  # Default to positive
                continue
            
            # Apply lookback if specified
            if lookback_periods is not None and len(asset_returns) > lookback_periods:
                asset_returns = asset_returns.tail(lookback_periods)
            
            # Calculate cumulative return
            cumulative_return = asset_returns.sum()
            
            # Assign direction based on cumulative return
            if cumulative_return < 0:
                directional_weights[asset] = -abs(abs_weight)  # Negative weight for short
                logger.debug(f"Assigned negative weight to {asset} (cumulative return: {cumulative_return:.6f})")
            else:
                directional_weights[asset] = abs(abs_weight)   # Positive weight for long
                logger.debug(f"Assigned positive weight to {asset} (cumulative return: {cumulative_return:.6f})")
        
        return directional_weights
    
    def calculate_lot_sizes(self, 
                          weights: Dict[str, float],
                          total_lot_size: float,
                          min_lot_size: float = 0.01) -> Dict[str, float]:
        """
        Calculate actual lot sizes based on normalized weights
        
        Args:
            weights: Dictionary with normalized weights
            total_lot_size: Total lot size to allocate
            min_lot_size: Minimum lot size per position
            
        Returns:
            Dictionary mapping assets to lot sizes
        """
        if not weights:
            logger.warning("Empty weights provided for lot size calculation")
            return {}
        
        lot_sizes = {}
        
        for asset, weight in weights.items():
            # Calculate lot size (preserving direction)
            lot_size = total_lot_size * weight
            
            # Apply minimum lot size constraint
            if abs(lot_size) < min_lot_size:
                if lot_size >= 0:
                    lot_size = min_lot_size
                else:
                    lot_size = -min_lot_size
                logger.debug(f"Applied minimum lot size to {asset}: {lot_size}")
            
            lot_sizes[asset] = lot_size
        
        # Verify total allocation
        total_allocated = sum(abs(lot) for lot in lot_sizes.values())
        allocation_ratio = total_allocated / total_lot_size if total_lot_size > 0 else 0
        
        logger.info(f"Lot size allocation: {total_allocated:.2f} / {total_lot_size:.2f} ({allocation_ratio:.1%})")
        
        return lot_sizes
    
    def validate_weights(self, weights: Dict[str, float]) -> Tuple[bool, List[str]]:
        """
        Validate weight constraints
        
        Args:
            weights: Dictionary with weights to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        if not weights:
            errors.append("Empty weights dictionary")
            return False, errors
        
        # Check individual weight bounds
        for asset, weight in weights.items():
            abs_weight = abs(weight)
            if abs_weight < MIN_WEIGHT:
                errors.append(f"Weight for {asset} ({abs_weight:.6f}) below minimum ({MIN_WEIGHT})")
            elif abs_weight > MAX_WEIGHT:
                errors.append(f"Weight for {asset} ({abs_weight:.6f}) above maximum ({MAX_WEIGHT})")
        
        # Check sum of absolute weights
        abs_sum = sum(abs(weight) for weight in weights.values())
        if abs(abs_sum - 1.0) > WEIGHT_TOLERANCE:
            errors.append(f"Sum of absolute weights ({abs_sum:.6f}) not equal to 1.0")
        
        # Check for NaN or infinite values
        for asset, weight in weights.items():
            if not np.isfinite(weight):
                errors.append(f"Invalid weight for {asset}: {weight}")
        
        is_valid = len(errors) == 0
        return is_valid, errors
    
    def rebalance_weights(self, 
                        current_weights: Dict[str, float],
                        target_weights: Dict[str, float],
                        rebalance_threshold: float = 0.05) -> Dict[str, float]:
        """
        Calculate rebalancing trades based on current and target weights
        
        Args:
            current_weights: Current portfolio weights
            target_weights: Target portfolio weights
            rebalance_threshold: Minimum change threshold for rebalancing
            
        Returns:
            Dictionary with rebalancing trades (positive = buy, negative = sell)
        """
        if not current_weights or not target_weights:
            logger.warning("Empty weights provided for rebalancing")
            return {}
        
        rebalancing_trades = {}
        
        # Get all assets
        all_assets = set(current_weights.keys()) | set(target_weights.keys())
        
        for asset in all_assets:
            current_weight = current_weights.get(asset, 0.0)
            target_weight = target_weights.get(asset, 0.0)
            
            # Calculate required change
            weight_change = target_weight - current_weight
            
            # Apply rebalancing threshold
            if abs(weight_change) >= rebalance_threshold:
                rebalancing_trades[asset] = weight_change
                logger.debug(f"Rebalancing {asset}: {current_weight:.4f} -> {target_weight:.4f} (change: {weight_change:+.4f})")
        
        logger.info(f"Rebalancing required for {len(rebalancing_trades)} assets")
        return rebalancing_trades
    
    def optimize_weight_precision(self, 
                                weights: Dict[str, float],
                                precision: int = 4) -> Dict[str, float]:
        """
        Optimize weight precision while maintaining normalization
        
        Args:
            weights: Dictionary with weights to optimize
            precision: Number of decimal places
            
        Returns:
            Dictionary with optimized precision weights
        """
        if not weights:
            return {}
        
        # Round weights to specified precision
        rounded_weights = {
            asset: round(weight, precision)
            for asset, weight in weights.items()
        }
        
        # Check if normalization is still valid
        abs_sum = sum(abs(weight) for weight in rounded_weights.values())
        
        if abs(abs_sum - 1.0) > WEIGHT_TOLERANCE:
            # Re-normalize if rounding caused significant deviation
            logger.debug(f"Re-normalizing after precision optimization: {abs_sum:.6f} -> 1.0")
            rounded_weights = self.normalize_absolute_weights(rounded_weights)
            
            # Round again after normalization
            rounded_weights = {
                asset: round(weight, precision)
                for asset, weight in rounded_weights.items()
            }
        
        return rounded_weights
    
    def get_weight_statistics(self, weights: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate statistics for portfolio weights
        
        Args:
            weights: Dictionary with portfolio weights
            
        Returns:
            Dictionary with weight statistics
        """
        if not weights:
            return {}
        
        weight_values = list(weights.values())
        abs_weights = [abs(w) for w in weight_values]
        
        # Count long and short positions
        long_positions = sum(1 for w in weight_values if w > 0)
        short_positions = sum(1 for w in weight_values if w < 0)
        
        # Calculate concentration metrics
        max_abs_weight = max(abs_weights) if abs_weights else 0
        min_abs_weight = min(abs_weights) if abs_weights else 0
        
        # Herfindahl-Hirschman Index (concentration measure)
        hhi = sum(w**2 for w in abs_weights) if abs_weights else 0
        
        statistics = {
            'num_assets': len(weights),
            'long_positions': long_positions,
            'short_positions': short_positions,
            'long_ratio': long_positions / len(weights) if weights else 0,
            'max_abs_weight': max_abs_weight,
            'min_abs_weight': min_abs_weight,
            'weight_range': max_abs_weight - min_abs_weight,
            'avg_abs_weight': np.mean(abs_weights) if abs_weights else 0,
            'weight_std': np.std(abs_weights) if abs_weights else 0,
            'concentration_hhi': hhi,
            'effective_num_assets': 1 / hhi if hhi > 0 else 0,
            'sum_abs_weights': sum(abs_weights)
        }
        
        return statistics


# Numba-optimized functions for performance
@njit
def fast_normalize_weights(weights_array: np.ndarray, target_sum: float = 1.0) -> np.ndarray:
    """
    Fast weight normalization using Numba
    
    Args:
        weights_array: Array of weights
        target_sum: Target sum for absolute weights
        
    Returns:
        Normalized weights array
    """
    abs_sum = 0.0
    for w in weights_array:
        abs_sum += abs(w)
    
    if abs_sum == 0.0:
        return weights_array
    
    normalization_factor = target_sum / abs_sum
    
    normalized = np.empty_like(weights_array)
    for i in range(len(weights_array)):
        normalized[i] = weights_array[i] * normalization_factor
    
    return normalized


@njit
def fast_weight_validation(weights_array: np.ndarray, 
                          min_weight: float, 
                          max_weight: float,
                          tolerance: float) -> bool:
    """
    Fast weight validation using Numba
    
    Args:
        weights_array: Array of weights to validate
        min_weight: Minimum absolute weight
        max_weight: Maximum absolute weight
        tolerance: Tolerance for sum validation
        
    Returns:
        True if weights are valid
    """
    abs_sum = 0.0
    
    for w in weights_array:
        abs_w = abs(w)
        
        # Check bounds
        if abs_w < min_weight or abs_w > max_weight:
            return False
        
        # Check for invalid values
        if not (w == w):  # NaN check
            return False
        
        abs_sum += abs_w
    
    # Check sum
    if abs(abs_sum - 1.0) > tolerance:
        return False
    
    return True


# Convenience functions
def create_equal_weights(assets: List[str], total_weight: float = 1.0) -> Dict[str, float]:
    """Create equal weights for a list of assets"""
    if not assets:
        return {}
    
    weight_per_asset = total_weight / len(assets)
    return {asset: weight_per_asset for asset in assets}


def apply_weight_bounds(weights: Dict[str, float], 
                       min_weight: float = MIN_WEIGHT,
                       max_weight: float = MAX_WEIGHT) -> Dict[str, float]:
    """Apply weight bounds and re-normalize"""
    bounded_weights = {}
    
    for asset, weight in weights.items():
        abs_weight = abs(weight)
        sign = 1 if weight >= 0 else -1
        
        # Apply bounds
        bounded_abs_weight = max(min_weight, min(abs_weight, max_weight))
        bounded_weights[asset] = sign * bounded_abs_weight
    
    # Re-normalize
    manager = WeightManager()
    return manager.normalize_absolute_weights(bounded_weights)


if __name__ == "__main__":
    # Test the weight manager
    logging.basicConfig(level=logging.INFO)
    
    print("Testing Weight Manager...")
    
    # Create sample data
    assets = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'NZDUSD']
    
    # Test weight normalization
    manager = WeightManager()
    
    # Test 1: Basic normalization
    weights = {asset: np.random.uniform(0.1, 0.3) for asset in assets}
    normalized = manager.normalize_absolute_weights(weights)
    print(f"✓ Normalized weights: sum = {sum(abs(w) for w in normalized.values()):.6f}")
    
    # Test 2: Weight direction assignment
    dates = pd.date_range('2024-01-01', periods=100, freq='15T')
    returns = pd.DataFrame({
        asset: np.random.normal(0, 0.01, len(dates))
        for asset in assets
    }, index=dates)
    
    directional = manager.assign_weight_directions(normalized, returns)
    negative_count = sum(1 for w in directional.values() if w < 0)
    print(f"✓ Directional weights: {negative_count} negative weights assigned")
    
    # Test 3: Lot size calculation
    lot_sizes = manager.calculate_lot_sizes(directional, 10.0)
    total_lots = sum(abs(lot) for lot in lot_sizes.values())
    print(f"✓ Lot sizes calculated: total = {total_lots:.2f}")
    
    # Test 4: Weight validation
    is_valid, errors = manager.validate_weights(directional)
    print(f"✓ Weight validation: {'PASS' if is_valid else 'FAIL'}")
    if errors:
        for error in errors:
            print(f"  - {error}")
    
    # Test 5: Weight statistics
    stats = manager.get_weight_statistics(directional)
    print(f"✓ Weight statistics: {stats['long_positions']} long, {stats['short_positions']} short")
