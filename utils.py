"""
Utility Functions for Matrix QP
Common helper functions and utilities used across the application
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import json
import os

from config import CURRENCY_PAIRS, MARKET_TIMEZONE

# Configure logging
logger = logging.getLogger(__name__)


def validate_currency_pair(pair: str) -> bool:
    """
    Validate if a string is a valid currency pair format
    
    Args:
        pair: Currency pair string to validate
        
    Returns:
        bool: True if valid currency pair format
    """
    if not isinstance(pair, str):
        return False
    
    if len(pair) != 6:
        return False
    
    # Check if all characters are alphabetic
    if not pair.isalpha():
        return False
    
    # Check if it's in our supported pairs
    return pair.upper() in CURRENCY_PAIRS


def format_currency_pair(pair: str) -> str:
    """
    Format currency pair string consistently
    
    Args:
        pair: Currency pair string
        
    Returns:
        Formatted currency pair string
    """
    if not validate_currency_pair(pair):
        raise ValueError(f"Invalid currency pair: {pair}")
    
    return pair.upper()


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """
    Calculate percentage change between two values
    
    Args:
        old_value: Original value
        new_value: New value
        
    Returns:
        Percentage change
    """
    if old_value == 0:
        return 0.0 if new_value == 0 else float('inf')
    
    return ((new_value - old_value) / old_value) * 100


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Safely divide two numbers, returning default if denominator is zero
    
    Args:
        numerator: Numerator
        denominator: Denominator
        default: Default value if division by zero
        
    Returns:
        Division result or default
    """
    if denominator == 0:
        return default
    return numerator / denominator


def clean_dataframe(df: pd.DataFrame, 
                   drop_na: bool = True,
                   fill_method: Optional[str] = None) -> pd.DataFrame:
    """
    Clean DataFrame by handling missing values and outliers
    
    Args:
        df: DataFrame to clean
        drop_na: Whether to drop rows with NaN values
        fill_method: Method to fill NaN values ('ffill', 'bfill', 'mean', 'median')
        
    Returns:
        Cleaned DataFrame
    """
    df_clean = df.copy()
    
    # Handle infinite values
    df_clean = df_clean.replace([np.inf, -np.inf], np.nan)
    
    # Handle missing values
    if fill_method == 'ffill':
        df_clean = df_clean.fillna(method='ffill')
    elif fill_method == 'bfill':
        df_clean = df_clean.fillna(method='bfill')
    elif fill_method == 'mean':
        df_clean = df_clean.fillna(df_clean.mean())
    elif fill_method == 'median':
        df_clean = df_clean.fillna(df_clean.median())
    elif drop_na:
        df_clean = df_clean.dropna()
    
    return df_clean


def detect_outliers(data: pd.Series, method: str = 'iqr', threshold: float = 1.5) -> pd.Series:
    """
    Detect outliers in a data series
    
    Args:
        data: Data series
        method: Outlier detection method ('iqr', 'zscore')
        threshold: Threshold for outlier detection
        
    Returns:
        Boolean series indicating outliers
    """
    if method == 'iqr':
        Q1 = data.quantile(0.25)
        Q3 = data.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - threshold * IQR
        upper_bound = Q3 + threshold * IQR
        return (data < lower_bound) | (data > upper_bound)
    
    elif method == 'zscore':
        z_scores = np.abs((data - data.mean()) / data.std())
        return z_scores > threshold
    
    else:
        raise ValueError(f"Unknown outlier detection method: {method}")


def format_number(value: float, 
                 decimal_places: int = 4,
                 percentage: bool = False,
                 currency: bool = False) -> str:
    """
    Format number for display
    
    Args:
        value: Number to format
        decimal_places: Number of decimal places
        percentage: Whether to format as percentage
        currency: Whether to format as currency
        
    Returns:
        Formatted string
    """
    if pd.isna(value) or not np.isfinite(value):
        return "N/A"
    
    if percentage:
        return f"{value * 100:.{decimal_places}f}%"
    elif currency:
        return f"${value:,.{decimal_places}f}"
    else:
        return f"{value:.{decimal_places}f}"


def create_time_range(start_time: datetime,
                     end_time: datetime,
                     freq: str = '15T') -> pd.DatetimeIndex:
    """
    Create a time range with specified frequency

    Args:
        start_time: Start time
        end_time: End time
        freq: Frequency string (e.g., '15T' for 15 minutes)

    Returns:
        DatetimeIndex with time range
    """
    return pd.date_range(start=start_time, end=end_time, freq=freq, tz=MARKET_TIMEZONE)


def calculate_time_range_for_period(time_period: str) -> Tuple[datetime, datetime]:
    """
    Calculate start and end times based on selected time period

    Args:
        time_period: Time period string ('2h', '4h', '8h', '24h', '48h', '72h', '120h')

    Returns:
        Tuple of (start_time, end_time) in market timezone
    """
    now = datetime.now(MARKET_TIMEZONE)

    # Parse the time period
    if time_period.endswith('h'):
        hours = int(time_period[:-1])
    else:
        raise ValueError(f"Invalid time period format: {time_period}")

    # Calculate end time (current time)
    end_time = now

    # Handle different time periods
    if hours <= 24:
        # For periods <= 24h: previous X hours + minutes of current hour
        if hours == 24:
            # Special case for 24h: from 00:00 today (current behavior)
            if now.weekday() >= 5:  # Weekend
                # Use Friday's data
                days_back = now.weekday() - 4  # Days back to Friday
                start_time = now.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=days_back)
                end_time = start_time.replace(hour=23, minute=59, second=59)
            else:
                # Weekday: from 00:00 today
                start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            # For 2h, 4h, 8h: previous X hours + minutes of current hour
            start_time = now - timedelta(hours=hours)

            # If start time falls on weekend, adjust to Friday
            if start_time.weekday() >= 5:
                days_to_friday = start_time.weekday() - 4
                start_time = start_time - timedelta(days=days_to_friday)
                start_time = start_time.replace(hour=23, minute=59, second=59)
    else:
        # For periods > 24h: previous X weekdays (excluding weekends) + current day
        days = hours // 24

        # Start from current day and go back, counting only weekdays
        current_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
        weekdays_needed = days

        # If today is weekend, start from Friday
        if current_date.weekday() >= 5:  # Weekend
            days_to_friday = current_date.weekday() - 4
            current_date = current_date - timedelta(days=days_to_friday)
            end_time = current_date.replace(hour=23, minute=59, second=59)

        # Count back weekdays only
        weekdays_counted = 0
        check_date = current_date

        while weekdays_counted < weekdays_needed:
            check_date = check_date - timedelta(days=1)
            # Only count weekdays (Monday=0 to Friday=4)
            if check_date.weekday() < 5:
                weekdays_counted += 1

        start_time = check_date

        logger.info(f"Multi-day period {time_period}: excluding weekends, using {weekdays_needed} weekdays")

    logger.info(f"Time period {time_period}: {start_time} to {end_time}")
    return start_time, end_time


def align_dataframes(*dataframes: pd.DataFrame, 
                    method: str = 'inner') -> List[pd.DataFrame]:
    """
    Align multiple DataFrames to common index
    
    Args:
        dataframes: DataFrames to align
        method: Alignment method ('inner', 'outer', 'left', 'right')
        
    Returns:
        List of aligned DataFrames
    """
    if not dataframes:
        return []
    
    if len(dataframes) == 1:
        return list(dataframes)
    
    # Find common index
    if method == 'inner':
        common_index = dataframes[0].index
        for df in dataframes[1:]:
            common_index = common_index.intersection(df.index)
    elif method == 'outer':
        common_index = dataframes[0].index
        for df in dataframes[1:]:
            common_index = common_index.union(df.index)
    else:
        # Use first DataFrame's index for left/right
        common_index = dataframes[0].index
    
    # Align all DataFrames
    aligned = []
    for df in dataframes:
        aligned_df = df.reindex(common_index)
        aligned.append(aligned_df)
    
    return aligned


def calculate_rolling_statistics(data: pd.Series,
                               window: int,
                               statistics: List[str] = ['mean', 'std']) -> pd.DataFrame:
    """
    Calculate rolling statistics for a data series

    Args:
        data: Data series
        window: Rolling window size
        statistics: List of statistics to calculate

    Returns:
        DataFrame with rolling statistics
    """
    result = pd.DataFrame(index=data.index)

    for stat in statistics:
        if stat == 'mean':
            result[f'rolling_{stat}'] = data.rolling(window).mean()
        elif stat == 'std':
            result[f'rolling_{stat}'] = data.rolling(window).std()
        elif stat == 'min':
            result[f'rolling_{stat}'] = data.rolling(window).min()
        elif stat == 'max':
            result[f'rolling_{stat}'] = data.rolling(window).max()
        elif stat == 'median':
            result[f'rolling_{stat}'] = data.rolling(window).median()
        elif stat == 'var':
            result[f'rolling_{stat}'] = data.rolling(window).var()
        elif stat == 'skew':
            result[f'rolling_{stat}'] = data.rolling(window).skew()
        elif stat == 'kurt':
            result[f'rolling_{stat}'] = data.rolling(window).kurt()

    return result


def calculate_rsi(data: pd.Series, window: int = 14) -> pd.Series:
    """
    Calculate Relative Strength Index (RSI)

    Args:
        data: Price data series (cumulative returns)
        window: RSI calculation window (default 14)

    Returns:
        RSI values series
    """
    # Calculate price changes
    delta = data.diff()

    # Separate gains and losses
    gains = delta.where(delta > 0, 0)
    losses = -delta.where(delta < 0, 0)

    # Calculate average gains and losses using exponential moving average
    avg_gains = gains.ewm(span=window, adjust=False).mean()
    avg_losses = losses.ewm(span=window, adjust=False).mean()

    # Calculate relative strength
    rs = avg_gains / avg_losses

    # Calculate RSI
    rsi = 100 - (100 / (1 + rs))

    return rsi


def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """
    Calculate Average Directional Index (ADX)

    Args:
        high: High price series
        low: Low price series
        close: Close price series
        window: ADX calculation window (default 14)

    Returns:
        ADX values series
    """
    # Calculate True Range (TR)
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

    # Calculate Directional Movement
    dm_plus = high.diff()
    dm_minus = low.diff() * -1

    # Set DM to 0 when movement is not in the direction
    dm_plus = dm_plus.where((dm_plus > dm_minus) & (dm_plus > 0), 0)
    dm_minus = dm_minus.where((dm_minus > dm_plus) & (dm_minus > 0), 0)

    # Calculate smoothed averages using exponential moving average
    atr = tr.ewm(span=window, adjust=False).mean()
    di_plus = (dm_plus.ewm(span=window, adjust=False).mean() / atr) * 100
    di_minus = (dm_minus.ewm(span=window, adjust=False).mean() / atr) * 100

    # Calculate Directional Index (DX)
    dx = (abs(di_plus - di_minus) / (di_plus + di_minus)) * 100

    # Calculate ADX as smoothed DX
    adx = dx.ewm(span=window, adjust=False).mean()

    return adx


def calculate_adx_from_returns(data: pd.Series, window: int = 14) -> pd.Series:
    """
    Calculate ADX from a single price/returns series by creating synthetic OHLC data

    Args:
        data: Price or cumulative returns series
        window: ADX calculation window (default 14)

    Returns:
        ADX values series
    """
    # For a single series, we'll create synthetic OHLC data
    # This is a simplified approach for demonstration purposes

    # Use the data as close prices
    close = data

    # Create synthetic high/low based on volatility
    rolling_std = data.rolling(window=5).std()
    high = close + rolling_std * 0.5
    low = close - rolling_std * 0.5

    return calculate_adx(high, low, close, window)


def save_to_json(data: Any, filepath: str, indent: int = 2) -> bool:
    """
    Save data to JSON file
    
    Args:
        data: Data to save
        filepath: File path
        indent: JSON indentation
        
    Returns:
        bool: True if successful
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=indent, default=str)
        
        logger.debug(f"Data saved to {filepath}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to save data to {filepath}: {str(e)}")
        return False


def load_from_json(filepath: str) -> Optional[Any]:
    """
    Load data from JSON file
    
    Args:
        filepath: File path
        
    Returns:
        Loaded data or None if failed
    """
    try:
        if not os.path.exists(filepath):
            logger.warning(f"File not found: {filepath}")
            return None
        
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        logger.debug(f"Data loaded from {filepath}")
        return data
        
    except Exception as e:
        logger.error(f"Failed to load data from {filepath}: {str(e)}")
        return None


def create_backup(data: Any, 
                 backup_dir: str = 'backups',
                 prefix: str = 'backup') -> str:
    """
    Create a timestamped backup of data
    
    Args:
        data: Data to backup
        backup_dir: Backup directory
        prefix: Filename prefix
        
    Returns:
        Backup filepath
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{prefix}_{timestamp}.json"
    filepath = os.path.join(backup_dir, filename)
    
    if save_to_json(data, filepath):
        logger.info(f"Backup created: {filepath}")
        return filepath
    else:
        logger.error(f"Failed to create backup: {filepath}")
        return ""


def validate_weights(weights: Dict[str, float], tolerance: float = 1e-6) -> Tuple[bool, str]:
    """
    Validate portfolio weights
    
    Args:
        weights: Dictionary of weights
        tolerance: Tolerance for sum validation
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not weights:
        return False, "Empty weights dictionary"
    
    # Check for invalid values
    for asset, weight in weights.items():
        if not isinstance(weight, (int, float)):
            return False, f"Invalid weight type for {asset}: {type(weight)}"
        
        if not np.isfinite(weight):
            return False, f"Invalid weight value for {asset}: {weight}"
    
    # Check sum of absolute weights
    abs_sum = sum(abs(weight) for weight in weights.values())
    if abs(abs_sum - 1.0) > tolerance:
        return False, f"Sum of absolute weights ({abs_sum:.6f}) not equal to 1.0"
    
    return True, "Weights are valid"


def calculate_correlation_matrix(data: pd.DataFrame, 
                               method: str = 'pearson',
                               min_periods: int = 30) -> pd.DataFrame:
    """
    Calculate correlation matrix with minimum periods requirement
    
    Args:
        data: DataFrame with time series data
        method: Correlation method
        min_periods: Minimum periods for correlation calculation
        
    Returns:
        Correlation matrix
    """
    return data.corr(method=method, min_periods=min_periods)


def get_market_hours_mask(timestamps: pd.DatetimeIndex,
                         start_hour: int = 0,
                         end_hour: int = 23) -> pd.Series:
    """
    Create a mask for market hours
    
    Args:
        timestamps: DatetimeIndex
        start_hour: Market start hour
        end_hour: Market end hour
        
    Returns:
        Boolean series for market hours
    """
    hours = timestamps.hour
    return (hours >= start_hour) & (hours <= end_hour)


def resample_data(data: pd.DataFrame, 
                 freq: str,
                 agg_method: str = 'last') -> pd.DataFrame:
    """
    Resample time series data to different frequency
    
    Args:
        data: DataFrame to resample
        freq: Target frequency
        agg_method: Aggregation method
        
    Returns:
        Resampled DataFrame
    """
    if agg_method == 'last':
        return data.resample(freq).last()
    elif agg_method == 'first':
        return data.resample(freq).first()
    elif agg_method == 'mean':
        return data.resample(freq).mean()
    elif agg_method == 'ohlc':
        return data.resample(freq).ohlc()
    else:
        raise ValueError(f"Unknown aggregation method: {agg_method}")


class PerformanceTimer:
    """Context manager for timing code execution"""
    
    def __init__(self, name: str = "Operation"):
        self.name = name
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        logger.debug(f"Starting {self.name}...")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        logger.debug(f"{self.name} completed in {duration:.3f} seconds")
    
    @property
    def duration(self) -> float:
        """Get duration in seconds"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0


def memory_usage_mb() -> float:
    """Get current memory usage in MB"""
    try:
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except ImportError:
        logger.warning("psutil not available, cannot measure memory usage")
        return 0.0


if __name__ == "__main__":
    # Test utility functions
    print("Testing Matrix QP Utilities...")
    
    # Test currency pair validation
    assert validate_currency_pair("EURUSD") == True
    assert validate_currency_pair("INVALID") == False
    print("✓ Currency pair validation")
    
    # Test number formatting
    assert format_number(0.1234, 2) == "0.12"
    assert format_number(0.1234, 2, percentage=True) == "12.34%"
    print("✓ Number formatting")
    
    # Test performance timer
    with PerformanceTimer("Test operation") as timer:
        import time
        time.sleep(0.1)
    assert timer.duration >= 0.1
    print("✓ Performance timer")
    
    print("All utility tests passed!")
