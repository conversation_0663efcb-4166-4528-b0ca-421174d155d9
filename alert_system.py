"""
Alert System for Matrix QP Dashboard
Detects when multiple currency pairs start "taking off together" (birds getting ready to fly)
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import json
import os
import threading
import time
from dataclasses import dataclass
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)

class AlertType(Enum):
    TAKEOFF_DETECTED = "takeoff_detected"
    HIGH_DISPERSION = "high_dispersion"
    SYNCHRONIZED_MOVEMENT = "synchronized_movement"

@dataclass
class AlertEvent:
    """Data class for alert events"""
    timestamp: datetime
    alert_type: AlertType
    pairs_involved: List[str]
    magnitude: float
    message: str
    metadata: Dict

class TakeoffDetector:
    """
    Detects when multiple currency pairs start taking off together
    """
    
    def __init__(self, 
                 min_pairs_threshold: int = 5,
                 acceleration_threshold: float = 2.0,
                 magnitude_threshold: float = 0.5,
                 lookback_periods: int = 3):
        """
        Initialize the takeoff detector
        
        Args:
            min_pairs_threshold: Minimum number of pairs needed to trigger alert
            acceleration_threshold: Minimum acceleration factor for normalized returns
            magnitude_threshold: Minimum magnitude of normalized returns
            lookback_periods: Number of periods to look back for acceleration calculation
        """
        self.min_pairs_threshold = min_pairs_threshold
        self.acceleration_threshold = acceleration_threshold
        self.magnitude_threshold = magnitude_threshold
        self.lookback_periods = lookback_periods
        
        # State tracking
        self.last_normalized_returns = None
        self.last_alert_time = None
        self.alert_cooldown = timedelta(minutes=5)  # Prevent spam alerts
        
    def detect_takeoff(self, normalized_returns: pd.DataFrame) -> Optional[AlertEvent]:
        """
        Detect if multiple pairs are taking off together
        
        Args:
            normalized_returns: DataFrame with normalized returns (columns: pairs, index: timestamps)
            
        Returns:
            AlertEvent if takeoff detected, None otherwise
        """
        if normalized_returns.empty or len(normalized_returns) < self.lookback_periods:
            return None
            
        # Check cooldown period
        if (self.last_alert_time and 
            datetime.now() - self.last_alert_time < self.alert_cooldown):
            return None
            
        try:
            # Get latest data points
            latest_data = normalized_returns.tail(self.lookback_periods)
            
            # Calculate acceleration (rate of change of rate of change)
            accelerating_pairs = self._find_accelerating_pairs(latest_data)
            
            # Check if enough pairs are accelerating
            if len(accelerating_pairs) >= self.min_pairs_threshold:
                # Calculate overall magnitude
                magnitude = self._calculate_takeoff_magnitude(latest_data, accelerating_pairs)
                
                # Create alert event
                alert = AlertEvent(
                    timestamp=datetime.now(),
                    alert_type=AlertType.TAKEOFF_DETECTED,
                    pairs_involved=accelerating_pairs,
                    magnitude=magnitude,
                    message=f"🚀 TAKEOFF DETECTED: {len(accelerating_pairs)} pairs taking off together!",
                    metadata={
                        'acceleration_threshold': self.acceleration_threshold,
                        'magnitude_threshold': self.magnitude_threshold,
                        'pair_details': self._get_pair_details(latest_data, accelerating_pairs)
                    }
                )
                
                self.last_alert_time = datetime.now()
                return alert
                
        except Exception as e:
            logger.error(f"Error in takeoff detection: {str(e)}")
            
        return None
    
    def _find_accelerating_pairs(self, data: pd.DataFrame) -> List[str]:
        """Find pairs that are accelerating (increasing rate of change)"""
        accelerating_pairs = []
        
        for pair in data.columns:
            series = data[pair].dropna()
            if len(series) < self.lookback_periods:
                continue
                
            # Calculate first and second derivatives
            first_diff = series.diff()
            second_diff = first_diff.diff()
            
            # Check for acceleration and magnitude
            latest_acceleration = second_diff.iloc[-1] if not second_diff.empty else 0
            latest_magnitude = abs(series.iloc[-1]) if not series.empty else 0
            latest_velocity = abs(first_diff.iloc[-1]) if not first_diff.empty else 0
            
            # Conditions for takeoff:
            # 1. Positive acceleration (increasing rate of change)
            # 2. Sufficient magnitude
            # 3. Sufficient velocity
            meets_acceleration = latest_acceleration > self.acceleration_threshold
            meets_magnitude = latest_magnitude > self.magnitude_threshold
            meets_velocity = latest_velocity > self.magnitude_threshold * 0.5

            # Optional debug logging (uncomment for debugging)
            # logger.debug(f"{pair}: acc={latest_acceleration:.3f}>{self.acceleration_threshold} ({meets_acceleration}), "
            #              f"mag={latest_magnitude:.3f}>{self.magnitude_threshold} ({meets_magnitude}), "
            #              f"vel={latest_velocity:.3f}>{self.magnitude_threshold * 0.5:.3f} ({meets_velocity})")

            if meets_acceleration and meets_magnitude and meets_velocity:
                accelerating_pairs.append(pair)
                
        return accelerating_pairs
    
    def _calculate_takeoff_magnitude(self, data: pd.DataFrame, pairs: List[str]) -> float:
        """Calculate overall magnitude of the takeoff"""
        if not pairs:
            return 0.0
            
        # Get latest values for accelerating pairs
        latest_values = []
        for pair in pairs:
            if pair in data.columns:
                latest_val = data[pair].dropna().iloc[-1] if not data[pair].dropna().empty else 0
                latest_values.append(abs(latest_val))
        
        # Return average magnitude
        return np.mean(latest_values) if latest_values else 0.0
    
    def _get_pair_details(self, data: pd.DataFrame, pairs: List[str]) -> Dict:
        """Get detailed information about accelerating pairs"""
        details = {}
        
        for pair in pairs:
            if pair in data.columns:
                series = data[pair].dropna()
                if not series.empty:
                    details[pair] = {
                        'latest_value': float(series.iloc[-1]),
                        'velocity': float(series.diff().iloc[-1]) if len(series) > 1 else 0.0,
                        'acceleration': float(series.diff().diff().iloc[-1]) if len(series) > 2 else 0.0
                    }
        
        return details

class AlertManager:
    """
    Manages alert delivery and configuration
    """
    
    def __init__(self):
        self.alert_history = []
        self.max_history = 100
        self.notification_enabled = True
        self.sound_enabled = True
        self.email_enabled = False
        
        # Alert configuration file
        self.config_file = "alert_config.json"
        self.load_config()
        
    def load_config(self):
        """Load alert configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.notification_enabled = config.get('notification_enabled', True)
                    self.sound_enabled = config.get('sound_enabled', True)
                    self.email_enabled = config.get('email_enabled', False)
        except Exception as e:
            logger.error(f"Error loading alert config: {str(e)}")
    
    def save_config(self):
        """Save alert configuration to file"""
        try:
            config = {
                'notification_enabled': self.notification_enabled,
                'sound_enabled': self.sound_enabled,
                'email_enabled': self.email_enabled
            }
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving alert config: {str(e)}")
    
    def send_alert(self, alert: AlertEvent):
        """Send alert through configured channels"""
        try:
            # Add to history
            self.alert_history.append(alert)
            if len(self.alert_history) > self.max_history:
                self.alert_history.pop(0)
            
            # Log the alert
            logger.info(f"ALERT: {alert.message}")
            
            # Desktop notification
            if self.notification_enabled:
                self._send_desktop_notification(alert)
            
            # Sound alert
            if self.sound_enabled:
                self._play_alert_sound()
            
            # Email alert (if configured)
            if self.email_enabled:
                self._send_email_alert(alert)
                
        except Exception as e:
            logger.error(f"Error sending alert: {str(e)}")
    
    def _send_desktop_notification(self, alert: AlertEvent):
        """Send desktop notification"""
        try:
            import plyer
            plyer.notification.notify(
                title="Matrix QP Alert",
                message=alert.message,
                timeout=10
            )
        except ImportError:
            logger.warning("plyer not installed - desktop notifications disabled")
        except Exception as e:
            logger.error(f"Error sending desktop notification: {str(e)}")
    
    def _play_alert_sound(self):
        """Play alert sound"""
        try:
            import winsound
            # Play system sound
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        except ImportError:
            logger.warning("winsound not available - sound alerts disabled")
        except Exception as e:
            logger.error(f"Error playing alert sound: {str(e)}")
    
    def _send_email_alert(self, alert: AlertEvent):
        """Send email alert (placeholder for future implementation)"""
        # TODO: Implement email alerts if needed
        logger.info(f"Email alert would be sent: {alert.message}")
    
    def get_recent_alerts(self, limit: int = 10) -> List[AlertEvent]:
        """Get recent alerts"""
        return self.alert_history[-limit:] if self.alert_history else []
    
    def clear_history(self):
        """Clear alert history"""
        self.alert_history.clear()

# Global instances
takeoff_detector = TakeoffDetector()
alert_manager = AlertManager()

def check_for_takeoff(normalized_returns: pd.DataFrame) -> Optional[AlertEvent]:
    """
    Main function to check for takeoff patterns
    
    Args:
        normalized_returns: DataFrame with normalized returns
        
    Returns:
        AlertEvent if detected, None otherwise
    """
    return takeoff_detector.detect_takeoff(normalized_returns)

def send_alert(alert: AlertEvent):
    """Send alert through the alert manager"""
    alert_manager.send_alert(alert)

def get_recent_alerts(limit: int = 10) -> List[AlertEvent]:
    """Get recent alerts"""
    return alert_manager.get_recent_alerts(limit)
