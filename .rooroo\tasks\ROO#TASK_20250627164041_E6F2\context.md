# Task Context: Add Missing "Close All Positions" Button to matrix_QP Dashboard

## Goal
Add the "Close All MT5 Positions" button to the `../matrix_QP/dashboard.py` file, as it is currently missing according to user feedback.

## User Feedback
"The Close all MT5 positions button is still missing from the @matrix_qp project"
The user provided an image showing the correct implementation in the `matrix_QP - Tickmill` project, which should be used as a reference.

## Requirements
1.  **Modify `../matrix_QP/dashboard.py`:** Add the HTML and Dash components for the "Close All MT5 Positions" button.
2.  **Placement and Styling:** The button should be placed under the "Account Info" section and styled to match the reference image. The necessary CSS classes should already be in `../matrix_QP/assets/style.css` from a previous task.
3.  **Functionality:** Ensure the button is correctly linked to the `/close-all-positions` endpoint.

## Key File to Modify
*   `../matrix_QP/dashboard.py`

## Reference Files (Source of Changes)
*   `./dashboard.py` (from the `matrix_QP - Tickmill` project)